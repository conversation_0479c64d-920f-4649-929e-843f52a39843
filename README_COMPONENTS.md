# Hướng dẫn sử dụng các Component mới

## Tổng quan

Đã thêm thành công các component sau:

1. **ActivityChart** - Bi<PERSON><PERSON> đồ cột hiển thị số lượng hoạt động theo segment và tháng
2. **MissionTable** - Bảng hiển thị danh sách nhiệm vụ
3. **CustomerVisitTable** - Bảng hiển thị danh sách thăm hỏi khách hàng

## Cấu trúc file

### DTO và Interface

- `src/dto/mission.dto.ts` - Định nghĩa interface cho Mission, CustomerVisit, ActivitySegment

### Dữ liệu mẫu

- `src/common/constants/mockData.ts` - Chứa dữ liệu mẫu cho tất cả các component

### Components

- `src/components/ActivityChart/index.tsx` - Component biểu đồ Chart.js
- `src/views/main/Missions/components/MissionTable.tsx` - Component bảng nhiệm vụ
- `src/views/main/CustomerContact/components/CustomerVisitTable.tsx` - Component bảng thăm hỏi

### Pages

- `src/views/main/Missions/index.tsx` - Trang hiển thị biểu đồ và bảng nhiệm vụ
- `src/views/main/CustomerContact/index.tsx` - Trang hiển thị bảng thăm hỏi
- `src/views/main/Dashboard/index.tsx` - Trang demo hiển thị tất cả component

## Cách sử dụng

### 1. Biểu đồ ActivityChart

```tsx
import ActivityChart from './components/ActivityChart'
import { mockActivitySegments } from './common/constants/mockData'

;<ActivityChart data={mockActivitySegments} />
```

### 2. Bảng MissionTable

```tsx
import MissionTable from './views/main/Missions/components/MissionTable'
import { mockMissions } from './common/constants/mockData'

;<MissionTable
  data={mockMissions}
  loading={false}
  onView={(record) => console.log('View:', record)}
  onEdit={(record) => console.log('Edit:', record)}
  onDelete={(record) => console.log('Delete:', record)}
/>
```

### 3. Bảng CustomerVisitTable

```tsx
import CustomerVisitTable from './views/main/CustomerContact/components/CustomerVisitTable'
import { mockCustomerVisits } from './common/constants/mockData'

;<CustomerVisitTable
  data={mockCustomerVisits}
  loading={false}
  onView={(record) => console.log('View:', record)}
  onEdit={(record) => console.log('Edit:', record)}
  onDelete={(record) => console.log('Delete:', record)}
/>
```

## Các cột trong bảng

### MissionTable (Nhiệm vụ)

- STT
- ID hoạt động
- Tiêu đề
- Mô tả
- Loại (với màu sắc phân biệt)
- Trạng thái (với màu sắc phân biệt)
- Người tạo
- Ngày tạo
- Mã khách hàng
- Tên khách hàng
- Email
- Số điện thoại liên hệ
- Thao tác (Xem, Sửa, Xóa)

### CustomerVisitTable (Thăm hỏi khách hàng)

- STT
- ID hoạt động
- Tiêu đề
- Mô tả
- Loại (với màu sắc phân biệt)
- Trạng thái (với màu sắc phân biệt)
- Người tạo
- Ngày tạo
- Mã khách hàng
- Tên khách hàng
- Email
- Số điện thoại liên hệ
- Thao tác (Xem, Sửa, Xóa)

## Biểu đồ ActivityChart

Biểu đồ cột hiển thị:

- Trục X: Tháng (T1/2024, T2/2024, ...)
- Trục Y: Số lượng hoạt động
- Legend: Các segment khách hàng (VIP, Thường, Tiềm năng)
- Màu sắc phân biệt cho từng segment

## Dữ liệu mẫu

### Missions (8 bản ghi)

- Các loại: Tư vấn, Khảo sát, Đào tạo, Bảo trì, Triển khai, Hỗ trợ, Đánh giá, Cập nhật
- Các trạng thái: Hoàn thành, Đang thực hiện, Chờ xử lý

### CustomerVisits (8 bản ghi)

- Các loại: Thăm hỏi, Khảo sát, Giới thiệu, Hỗ trợ, Đánh giá, Tư vấn, Khắc phục
- Các trạng thái: Hoàn thành, Đang thực hiện, Chờ xử lý

### ActivitySegments (18 bản ghi)

- 6 tháng (T1-T6/2024)
- 3 segment: Khách hàng VIP, Khách hàng thường, Khách hàng tiềm năng

## Truy cập

Các trang có thể truy cập qua:

- `/missions` - Trang Nhiệm vụ
- `/customer-contact` - Trang Thăm hỏi khách hàng

## Tính năng

- **Responsive**: Tất cả component đều responsive
- **Pagination**: Bảng có phân trang tự động
- **Sorting**: Có thể sắp xếp theo các cột
- **Filtering**: Có thể lọc dữ liệu
- **Actions**: Các nút thao tác (Xem, Sửa, Xóa)
- **Status Colors**: Màu sắc phân biệt cho trạng thái và loại
- **Loading States**: Hỗ trợ trạng thái loading
- **Error Handling**: Xử lý lỗi cơ bản
