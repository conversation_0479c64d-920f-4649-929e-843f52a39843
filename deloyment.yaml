# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ape-chain-admin-dev
  namespace: ape-chain-dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ape-chain-admin-dev
  template:
    metadata:
      labels:
        app: ape-chain-admin-dev
    spec:
      containers:
        - name: ape-chain-admin-dev
          image: 077293829360.dkr.ecr.ap-southeast-1.amazonaws.com/ape-chain-admin-dev:latest
          ports:
            - containerPort: 80
          volumeMounts:
            - mountPath: /etc/localtime
              name: tz-config
      volumes:
        - name: tz-config
          hostPath:
            path: /usr/share/zoneinfo/Asia/Ho_Chi_Minh
---
# service.yaml
apiVersion: v1
kind: Service
metadata:
  name: ape-chain-admin-dev
  namespace: ape-chain-dev
  labels:
    run: ape-chain-admin-dev
spec:
  type: ClusterIP
  ports:
    - port: 80
      protocol: TCP
      targetPort: 80
  selector:
    app: ape-chain-admin-dev
