import { FC } from 'react'
import { useNavigate, useRoutes } from 'react-router'
import { RouteObject } from 'react-router'
import { navService, toastService } from '~/services'
import LoginView from './views/auth/LoginView'
import MainLayout from '~/layouts/MainLayout'
import { MainRouter } from './views/routers/main.router'

const appRouter: RouteObject[] = [
  {
    path: 'login',
    element: <LoginView />
  },

  {
    path: '/',
    element: <MainLayout />,
    children: MainRouter() as RouteObject[]
  }
]
const AppRouter: FC = ({ ...props }) => {
  const navigate = useNavigate()
  navService.initRefNav(navigate)
  toastService.initNavigation(navigate)
  const element = useRoutes(appRouter)
  return element
}

export default AppRouter
