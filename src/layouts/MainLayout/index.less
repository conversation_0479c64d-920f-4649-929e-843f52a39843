:root {
  --primary-color: oklch(79.5% 0.184 86.047);
  --primary-bg-color: oklch(98.7% 0.026 102.212);
}
.layout-page {
  height: 100%;
  min-height: 100vh;

  &-header {
    padding: 0 !important;
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 9;
    border-bottom: 1px solid #e8e8e8;

    svg {
      width: 20px;
      height: 20px;
      cursor: pointer;
    }

    &-main {
      padding: 0 15px;
      flex: 1;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .logo {
      height: 64px;
      width: 200px;
      box-sizing: border-box;
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 9;

      img {
        width: 58px;
        height: 58px;
      }
    }
  }

  &-sider {
    box-sizing: border-box;
    margin-bottom: 10px;
  }

  &-content {
    display: flex;
    flex-direction: column;
    flex: 1;

    > :nth-child(1) .ant-tabs-bar {
      padding: 6px 0 0;
    }

    > :nth-child(2) {
      flex: auto;
      overflow: auto;
      padding: 6px;
      box-sizing: border-box;

      .innerText {
        padding: 24px;
        border-radius: 2px;
        display: block;
        line-height: 32px;
        font-size: 16px;
      }
    }
  }

  &-footer {
    text-align: center;
    padding: 14px 20px;
    font-size: 12px;
  }

  .actions {
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    > * {
      margin-left: 20px;
      height: 100%;
      display: flex;
      align-items: center;

      .notice {
        display: block;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 22px;
        height: 22px;
        cursor: pointer;
      }
    }
  }

  .user-action {
    cursor: pointer;
  }

  .user-avator {
    display: block;
    width: 40px;
    height: 40px;
    border-radius: 50%;
  }
}

body[theme-mode='dark'] {
  .layout-page-header {
    box-shadow: none;
  }
}

.layout-page-sider-menu {
  border-right: none !important;
}

.layout-page-sider {
  width: 200px !important;
  height: calc(100vh - 64px);
  overflow-y: auto;
}

// Customize scroll .layout-page-sider
.layout-page-sider::-webkit-scrollbar {
  width: 4px;
}

.layout-page-sider::-webkit-scrollbar-thumb {
  border-radius: 3px;
  background: rgba(26, 146, 244, 0.283);
}

.layout-page-sider::-webkit-scrollbar-thumb:hover {
  background: rgb(26, 146, 244);
}

.ant-menu-inline-collapsed {
  width: 79px !important;
}

.notice-description {
  font-size: 12px;

  &-datetime {
    margin-top: 4px;
    line-height: 1.5;
  }
}

.notice-title {
  display: flex;
  justify-content: space-between;
}

.tagsView-extra {
  height: 100%;
  width: 50px;
  cursor: pointer;
  display: block;
  line-height: 40px;
  text-align: center;
}

.themeSwitch {
  position: fixed;
  right: 32px;
  bottom: 102px;
  cursor: pointer;

  > span {
    display: block;
    text-align: center;
    background: #fff;
    border-radius: 50%;
    box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12),
      0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
    width: 44px;
    height: 44px;
    line-height: 44px;
    font-size: 22px;
    z-index: 10001;
  }
}

.theme-color-content {
  display: flex;

  .theme-color-block {
    width: 20px;
    height: 20px;
    margin-right: 8px;
    color: #fff;
    font-weight: 700;
    text-align: center;
    border-radius: 2px;
    cursor: pointer;
    border-radius: 2px;

    &:last-child {
      margin-right: 0;
    }
  }
}

// .ant-menu-light .ant-menu-item-selected {
//   color: var(--primary-color);
//   background-color: var(--primary-bg-color);

//   span {
//     color: var(--primary-color) !important;
//   }
// }

// .ant-tabs-tab {
//   border-radius: 8px 8px 8px 8px !important;
// }

.ant-tabs-nav {
  margin: 0px 0px 0px 0px !important;
  padding: 0px 0px 10px 0px !important;
}

// .ant-tabs .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
//   color: var(--primary-color);
// }

// .ant-switch.ant-switch-checked {
//   background: var(--primary-color);
// }

// .ant-tabs .ant-tabs-tab:hover {
//   color: var(--primary-color);
// }

.ant-typography.css-dev-only-do-not-override-lhscom {
  font-size: 14px !important;
}

.ant-table-body {
  overflow: hidden !important;
}
