import type { FC } from 'react'
import { useState } from 'react'
import { DeleteOutlined, PlusOutlined } from '@ant-design/icons'
import { But<PERSON>, Tooltip } from 'antd'
import { useLayoutConfig } from '~/stores/layoutConfig'
import { CreateOrEditNewsView } from '~/views/main/settings/news/component/CreateOrEditNewsView'
import { CreateOrEditSubscriptionPlanView } from '~/views/main/settings/subscription-plan/component/CreateOrEditSubscriptionPlanView'
import { CreateOrEditTermsView } from '~/views/main/settings/terms-and-privacy/component/CreateOrEditTermsView'

// Map tab titles to modal types
const MODAL_MAP = {
  //List các module có sử dụng CREATE
  // 'list-product': 'product',
  'settings/plan-service-list': 'plan-service-list',
  'settings/news': 'news',
  'settings/terms-and-privacy': 'terms-and-privacy'
} as const

type ModalType = (typeof MODAL_MAP)[keyof typeof MODAL_MAP]

const TabsAction: FC = () => {
  const { tabs, selectedKey, removeAllTab } = useLayoutConfig()
  const [openModal, setOpenModal] = useState<ModalType | null>(null)

  const currentTab = tabs.find((tab) => tab.key === selectedKey)
  const modalType = currentTab?.rootPath ? MODAL_MAP[currentTab.rootPath] : null

  const handlePress = () => {
    if (modalType) {
      setOpenModal(modalType)
    } else {
      console.log('No modal defined for this tab:', currentTab?.title)
    }
  }

  const handleDelete = () => {
    removeAllTab()
  }

  const handleCloseModal = () => {
    setOpenModal(null)
  }

  const handleSuccess = async () => {
    // console.log(`${currentTab?.title} created successfully!`)
    // Có thể refresh data hoặc thực hiện actions khác
    switch (modalType) {
      case 'news':
        break
      default:
        break
    }
    handleCloseModal()
  }

  // ẩn button create nếu là loyalty, report
  const hideButtonCreate = currentTab?.key.includes('loyalty') || currentTab?.key.includes('report')

  return (
    <>
      <div style={{ display: 'flex', gap: 8 }}>
        {currentTab && <Button onClick={handleDelete} htmlType='submit' icon={<DeleteOutlined />} style={{ display: 'block' }} />}

        {!hideButtonCreate && (
          <Tooltip
            title={
              currentTab?.path === 'plan-service-list'
                ? 'Nếu là gói dùng thử thì tick chọn checkbox "Dùng thử"  không cần nhập giá (hệ thống tự điền = 0) Nếu không phải là gói dùng thử phải nhập 1 trong 2 giá bán (tháng/năm)'
                : ''
            }>
            <Button onClick={handlePress} type='primary' htmlType='submit' icon={<PlusOutlined />} style={{ display: modalType ? 'block' : 'none' }}>
              {currentTab?.createTitle || ''}
            </Button>
          </Tooltip>
        )}

        <CreateOrEditSubscriptionPlanView open={openModal === 'plan-service-list'} onClose={handleCloseModal} />

        <CreateOrEditNewsView open={openModal === 'news'} onClose={handleCloseModal} />
        <CreateOrEditTermsView open={openModal === 'terms-and-privacy'} onClose={handleCloseModal} />
      </div>
    </>
  )
}

export default TabsAction
