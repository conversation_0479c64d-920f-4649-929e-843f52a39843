import { BarChartOutlined, IdcardOutlined, QuestionCircleOutlined, SettingOutlined, ShoppingCartOutlined } from '@ant-design/icons'
import { IRouter } from '~/routers'
import { Outlet } from 'react-router-dom'

import subMenuSettings from './sub-menu/settings'
import subMenuSalesChildren from './sub-menu/sales-management'
import { subMenuCustomerChildren } from './sub-menu'
import AdvisoryView from '../main/advisory'
import DashBoardView from '../main/dashboard'

const createRoute = (path: string, element: JSX.Element, title: string, icon: JSX.Element, isMenu = true, children: IRouter[] = []): IRouter => ({
  path,
  key: `route-${path}`,
  element,
  title,
  isMenu,
  icon,
  children
})

export const MainRouter = () => {
  return [
    createRoute('dashboard', <DashBoardView />, 'Báo cáo & Dashboard', <BarChartOutlined />, true),

    createRoute('member-management', <Outlet />, '<PERSON>u<PERSON><PERSON> lý khách hàng', <IdcardOutlined />, true, subMenuCustomerChildren),

    createRoute('sales-management', <Outlet />, 'Quản lý bán hàng', <ShoppingCartOutlined />, true, subMenuSalesChildren),

    createRoute('advisory', <AdvisoryView />, 'Yêu cầu tư vấn', <QuestionCircleOutlined />, true),

    // createRoute(
    //   'customer-support',
    //   <Outlet />,
    //   'Chăm sóc khách hàng',
    //   <CustomerServiceOutlined />,
    //   true,
    //   subMenuCustomerSupport
    // ),

    // ! Chuyển vào menu Chăm sóc khách hàng
    // createRoute(
    //   '/complaint-management',
    //   <ComplaintManagementView />,
    //   'Xử lý khiếu nại',
    //   <UnorderedListOutlined />
    // ),

    createRoute('settings', <Outlet />, 'Thiết lập', <SettingOutlined />, true, subMenuSettings)
  ]
}
