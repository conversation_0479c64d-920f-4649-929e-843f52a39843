import { TransactionOutlined, UnorderedListOutlined } from '@ant-design/icons'
import { IRouter } from '~/routers'
import OrderListView from '../../main/sales-management/order-list'
import PaymentTransactionView from '~/views/main/sales-management/payment-transaction'
import OnChainTransactionView from '~/views/main/sales-management/onchain-transaction'

const createRoute = (
  path: string,
  element: JSX.Element,
  title: string,
  icon: JSX.Element,
  isMenu = true,
  children: IRouter[] = []
): IRouter => ({
  path,
  key: `route-${path}`,
  element,
  title,
  isMenu,
  icon,
  children
})

const createMenuItem = (
  path: string,
  element: JSX.Element,
  title: string,
  icon = <UnorderedListOutlined />,
  isMenu?: boolean
): IRouter => createRoute(path, element, title, icon, isMenu)

const subMenuSalesChildren = [
  {
    path: 'onchain-transactions',
    view: <OnChainTransactionView />,
    title: 'Danh sách giao dịch (On Chain) ',
    icon: <TransactionOutlined />,
    isMenu: true
  },

  {
    path: 'payment-transactions',
    view: <PaymentTransactionView />,
    title: 'Danh sách giao dịch (Payment) ',
    icon: <TransactionOutlined />,
    isMenu: true
  },
  {
    path: 'orders',
    view: <OrderListView />,
    title: 'Danh sách đơn hàng',
    icon: <TransactionOutlined />,
    isMenu: true
  }
].map((item) => createMenuItem(item.path, item.view, item.title, item.icon, item.isMenu))

export default subMenuSalesChildren
