import {
  FileExclamationOutlined,
  FileUnknownOutlined,
  HeartOutlined,
  UnorderedListOutlined
} from '@ant-design/icons'
import { IRouter } from '~/routers'

const createRoute = (
  path: string,
  element: JSX.Element,
  title: string,
  icon: JSX.Element,
  isMenu = true,
  children: IRouter[] = []
): IRouter => ({
  path,
  key: `route-${path}`,
  element,
  title,
  isMenu,
  icon,
  children
})

const createMenuItem = (
  path: string,
  element: JSX.Element,
  title: string,
  icon = <UnorderedListOutlined />,
  isMenu?: boolean
): IRouter => createRoute(path, element, title, icon, isMenu)

const subMenuCustomerChildren = [
  // Danh sách liên hệ
].map((item) => createMenuItem(item.path, item.view, item.title, item.icon))

export default subMenuCustomerChildren
