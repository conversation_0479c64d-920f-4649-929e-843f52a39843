import { UnorderedListOutlined, UserOutlined } from '@ant-design/icons'
import { IRouter } from '~/routers'
import ListMemberView from '~/views/main/member/member-list'

const createRoute = (
  path: string,
  element: JSX.Element,
  title: string,
  icon: JSX.Element,
  isMenu = true,
  children: IRouter[] = []
): IRouter => ({
  path,
  key: `route-${path}`,
  element,
  title,
  isMenu,
  icon,
  children
})

const createMenuItem = (
  path: string,
  element: JSX.Element,
  title: string,
  icon = <UnorderedListOutlined />,
  isMenu?: boolean
): IRouter => createRoute(path, element, title, icon, isMenu)

const subMenuChildren = [
  {
    path: 'list-member',
    title: 'Danh sách khách hàng',
    view: <ListMemberView />,
    icon: <UserOutlined />,
    isMenu: true
  }
  // Báo cáo KPI
].map((item) => createMenuItem(item.path, item.view, item.title, item.icon, item.isMenu))

export default subMenuChildren
