import { useState, useEffect } from "react";

export interface ScreenSize {
  width: number;
  height: number;
  isDesktop: boolean;
  isMobile: boolean;
  isTablet: boolean;
}

export interface ScreenBreakpoints {
  mobile: number;
  tablet: number;
  desktop: number;
}

const DEFAULT_BREAKPOINTS: ScreenBreakpoints = {
  mobile: 576,
  tablet: 768,
  desktop: 1024,
};

/**
 * Custom hook để lấy kích thước màn hình và detect device type
 * @param breakpoints Custom breakpoints (optional)
 * @returns ScreenSize object với thông tin kích thước và device type
 */
export const useScreenSize = (
  breakpoints: ScreenBreakpoints = DEFAULT_BREAKPOINTS
): ScreenSize => {
  const [screenSize, setScreenSize] = useState<ScreenSize>(() => {
    // Initial state khi server-side rendering
    if (typeof window === "undefined") {
      return {
        width: 0,
        height: 0,
        isDesktop: false,
        isMobile: true,
        isTablet: false,
      };
    }

    const width = window.innerWidth;
    const height = window.innerHeight;

    return {
      width,
      height,
      isDesktop: width >= breakpoints.desktop,
      isMobile: width < breakpoints.mobile,
      isTablet: width >= breakpoints.mobile && width < breakpoints.desktop,
    };
  });

  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;

      setScreenSize({
        width,
        height,
        isDesktop: width >= breakpoints.desktop,
        isMobile: width < breakpoints.mobile,
        isTablet: width >= breakpoints.mobile && width < breakpoints.desktop,
      });
    };

    // Đặt kích thước ban đầu
    handleResize();

    // Lắng nghe sự kiện resize
    window.addEventListener("resize", handleResize);

    // Cleanup function
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, [breakpoints.desktop, breakpoints.mobile]);

  return screenSize;
};

/**
 * Hook đơn giản chỉ để lấy kích thước màn hình desktop
 * @returns Object với width và height của màn hình
 */
export const useDesktopScreenSize = () => {
  const [size, setSize] = useState(() => {
    if (typeof window === "undefined") {
      return { width: 0, height: 0 };
    }
    return {
      width: window.innerWidth,
      height: window.innerHeight,
    };
  });

  useEffect(() => {
    const handleResize = () => {
      setSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    };

    // Đặt kích thước ban đầu
    handleResize();

    // Lắng nghe sự kiện resize
    window.addEventListener("resize", handleResize);

    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  return size;
};

/**
 * Hook để check xem có phải desktop không
 * @param minWidth Minimum width để được coi là desktop (default: 1024px)
 * @returns boolean
 */
export const useIsDesktop = (minWidth: number = 1024): boolean => {
  const [isDesktop, setIsDesktop] = useState(() => {
    if (typeof window === "undefined") return false;
    return window.innerWidth >= minWidth;
  });

  useEffect(() => {
    const handleResize = () => {
      setIsDesktop(window.innerWidth >= minWidth);
    };

    handleResize();
    window.addEventListener("resize", handleResize);

    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, [minWidth]);

  return isDesktop;
};

export default useScreenSize;
