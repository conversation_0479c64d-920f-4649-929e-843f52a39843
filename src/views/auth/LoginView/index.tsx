import React from 'react'
import { Button, Form, Input, Layout, Spin, Typography, ConfigProvider, theme } from 'antd'
import { useAuthStore } from '~/stores/authStore'
import { COLORS } from '~/common/constants'
import { loginAssets } from '~/assets'
import BaseCheckbox from '~/components/BaseCheckbox'
import { TEXT_SIZE } from '~/common/constants/size'

const { Title, Text } = Typography

// TypeScript interfaces
interface LoginFormValues {
  username: string
  password: string
  remember?: boolean
}

// Light theme config cố định cho login
const loginThemeConfig = {
  algorithm: theme.defaultAlgorithm,
  token: {
    colorPrimary: COLORS.PRIMARY_RGB,
    colorBgContainer: COLORS.WHITE,
    colorBgBase: COLORS.WHITE,
    colorText: COLORS.BLACK,
    colorTextBase: COLORS.BLACK
  },
  components: {
    Layout: {
      colorBgHeader: COLORS.WHITE,
      colorBgBody: COLORS.WHITE
    },
    Input: {
      colorBgContainer: '#2D3748',
      colorText: COLORS.WHITE,
      colorBorder: '#4A5568',
      colorTextPlaceholder: '#A0AEC0'
    },
    Button: {
      colorPrimary: '#3182CE'
    },
    Form: {
      labelColor: COLORS.WHITE
    }
  }
}

const LoginView: React.FC = () => {
  const { login, isLoading } = useAuthStore()
  const [form] = Form.useForm()
  const handleFinish = (values: LoginFormValues) => {
    login({
      username: values.username,
      password: values.password
    })
  }

  return (
    <ConfigProvider theme={loginThemeConfig}>
      <Layout style={styles.layout}>
        <Spin tip='Đang đăng nhập...' spinning={isLoading}>
          <div style={styles.container}>
            {/* Header với logo và slogan */}
            <div style={styles.header}>
              <div style={styles.logoSection}>
                <div style={styles.logoIcon}>🔗</div>
                <Text style={styles.slogan}>Kết nối đỉnh cao. Tích hợp blockchain liên mạch.</Text>
              </div>
              <Title style={styles.brandTitle}>ApexLink</Title>
            </div>

            {/* Form container */}
            <div style={styles.formContainer}>
              <Title style={styles.formTitle}>Chào mừng trở lại</Title>
              <Text style={styles.formSubtitle}>Nhập thông tin đăng nhập của bạn để tiếp tục</Text>

              <Form<LoginFormValues>
                name='loginForm'
                layout='vertical'
                initialValues={{ remember: true }}
                onFinish={handleFinish}
                autoComplete='off'
                size='large'
                style={styles.form}>
                <Form.Item
                  label='Tài khoản'
                  name='username'
                  style={styles.formItem}
                  rules={[
                    {
                      required: true,
                      message: 'Vui lòng nhập userame của bạn'
                    }
                  ]}>
                  <Input placeholder='<EMAIL>' />
                </Form.Item>

                <Form.Item
                  label='Mật khẩu'
                  name='password'
                  rules={[
                    {
                      required: true,
                      message: 'Vui lòng nhập mật khẩu của bạn'
                    },
                    {
                      min: 6,
                      message: 'Mật khẩu phải có ít nhất 6 ký tự'
                    }
                  ]}>
                  <Input.Password placeholder='Nhập mật khẩu' />
                </Form.Item>

                <div style={styles.rememberForgotRow}>
                  <Form.Item name='remember' valuePropName='checked' style={styles.rememberItem}>
                    <BaseCheckbox
                      label='Ghi nhớ đăng nhập'
                      checked={true}
                      onChange={() => {}}
                      sizeCheckbox='large'
                      sizeText='large'
                      styleText={{ color: COLORS.WHITE }}
                    />
                  </Form.Item>
                </div>

                <Button type='primary' style={styles.submitButton} loading={isLoading} htmlType='submit'>
                  Đăng nhập →
                </Button>
              </Form>
            </div>
          </div>
        </Spin>
      </Layout>
    </ConfigProvider>
  )
}

export default LoginView

// Styles với màu cố định
const styles: Record<string, React.CSSProperties> = {
  layout: {
    justifyContent: 'center',
    alignItems: 'center',
    background: 'linear-gradient(132deg, rgba(15, 23, 42, 1) 0%, rgba(30, 58, 138, 1) 50%, rgba(15, 23, 42, 1) 100%)',

    minHeight: '100vh',
    padding: '20px',
    width: '100%'
  },
  container: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    width: 500
  },
  header: {
    textAlign: 'center',
    marginBottom: 20,
    color: COLORS.WHITE
  },
  logoSection: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
    gap: 8
  },
  logoIcon: {
    fontSize: '20px'
  },
  slogan: {
    color: '#A0AEC0',
    fontSize: '14px'
  },
  brandTitle: {
    color: COLORS.WHITE,
    fontSize: '48px',
    fontWeight: 'bold',
    margin: '0 0 8px 0'
  },
  subtitle: {
    color: '#3182CE',
    fontSize: '18px',
    fontWeight: '500',
    margin: '0 0 8px 0',
    display: 'block'
  },
  description: {
    color: '#A0AEC0',
    fontSize: '14px',
    margin: 0
  },
  formContainer: {
    backgroundColor: 'rgba(45, 55, 72, 0.5)',
    backdropFilter: 'blur(10px)',
    padding: '32px 24px',
    borderRadius: 12,
    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)',
    width: '100%',
    color: COLORS.WHITE
  },
  formTitle: {
    textAlign: 'center',
    marginBottom: 8,
    color: COLORS.WHITE,
    fontSize: '24px',
    fontWeight: '600'
  },
  formSubtitle: {
    textAlign: 'center',
    marginBottom: 32,
    color: '#A0AEC0',
    fontSize: '14px',
    display: 'block'
  },
  form: {
    width: '100%'
  },
  formItem: {
    marginBottom: 20
  },
  rememberForgotRow: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 24
  },
  rememberItem: {
    marginBottom: 0
  },
  forgotPassword: {
    color: '#3182CE',
    cursor: 'pointer',
    fontSize: '14px'
  },
  submitButton: {
    backgroundColor: '#3182CE',
    borderColor: '#3182CE',
    width: '100%',
    height: 44,
    borderRadius: 8,
    fontSize: '16px',
    fontWeight: '500'
  },
  registerSection: {
    textAlign: 'center',
    marginTop: 24,
    paddingTop: 24,
    borderTop: '1px solid #4A5568'
  },
  registerText: {
    color: '#A0AEC0',
    fontSize: '14px'
  },
  registerLink: {
    color: '#3182CE',
    cursor: 'pointer',
    fontWeight: '500'
  },
  footer: {
    textAlign: 'center',
    marginTop: 32,
    padding: '0 20px'
  },
  footerText: {
    color: '#A0AEC0',
    fontSize: '12px',
    lineHeight: '1.5'
  },
  footerLink: {
    color: '#3182CE',
    cursor: 'pointer'
  }
}
