import { FC, useEffect, useState } from 'react'
import { Card, Col, Empty, Row, Spin } from 'antd'
import { EthereumCircleColorful, BSCCircleColorful } from '@ant-design/web3-icons'
import BaseView from '~/components/BaseView'
import { useDashBoard } from './Hooks/useDashBoard'
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, ArcElement, PointElement, LineElement } from 'chart.js'
import dayjs from 'dayjs'
import { DollarCircleOutlined, ShoppingCartOutlined, ThunderboltOutlined, UserOutlined } from '@ant-design/icons'

import CustomerDashBoardView from './components/CustomerDashboard'
import OrderDashBoardView from './components/OrderDashboard'
import PackageDashBoardView from './components/PackageDashboard'
import PaymentDashBoardView from './components/PaymentDashboard'
import RevenueDashBoardView from './components/RevenueDashboard'
import RevenuePackageDashBoardView from './components/RevenuePackageDashboard'
import PaymentOnChainDashBoardView from './components/PaymentOnChainDashboard'

type IProps = {}
ChartJS.register(CategoryScale, LinearScale, ArcElement, BarElement, CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend)

const DashBoardView: FC<IProps> = () => {
  const [selectedYear, setSelectedYear] = useState(dayjs().year())
  const [selectedMonth, setSelectedMonth] = useState<number | undefined>(undefined)

  const { getRevenueStats, revenueStats, totalCustomer, getTotalCustomer, totalOrder, getTotalOrder, totalRevenue, getTotalRevenue, isLoading } =
    useDashBoard()

  const fetchStatsData = (year: number, month?: number) => {
    Promise.all([getRevenueStats(year, month), getTotalCustomer(), getTotalOrder(), getTotalRevenue()]).catch((err) => {
      console.log('err', err)
    })
  }

  useEffect(() => {
    fetchStatsData(selectedYear, selectedMonth)
  }, [selectedYear, selectedMonth])

  return (
    <BaseView>
      <Spin spinning={isLoading}>
        <Row gutter={16}>
          {/* Số lượng khách hàng */}
          <Col span={6} style={{ marginBottom: 20, display: 'flex', justifyContent: 'center' }}>
            <Card
              bordered={false}
              style={{
                width: 500,
                background: '#a3cefaff',
                boxShadow: '0 4px 16px #879bfd44',
                borderRadius: 18
              }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
                <div
                  style={{
                    background: '#2563eb',
                    borderRadius: 16,
                    width: 48,
                    height: 48,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                  <UserOutlined style={{ fontSize: 28, color: '#fff' }} />
                </div>
                <div>
                  <div style={{ color: '#2563eb', fontWeight: 650, fontSize: 16, marginBottom: 6 }}>Số lượng khách hàng</div>
                  <div style={{ color: '#111827', fontWeight: 700, fontSize: 32 }}>{totalCustomer.total || 0}</div>
                </div>
              </div>
            </Card>
          </Col>
          {/* Tổng doanh thu */}
          <Col span={6} style={{ marginBottom: 20, display: 'flex', justifyContent: 'center' }}>
            <Card
              bordered={false}
              style={{
                width: 500,
                background: '#cbb4ffff',
                boxShadow: '0 4px 16px #d8b4fe44',
                borderRadius: 18
              }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
                <div
                  style={{
                    background: '#a21caf',
                    borderRadius: 16,
                    width: 48,
                    height: 48,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                  <ShoppingCartOutlined style={{ fontSize: 28, color: '#fff' }} />
                </div>
                <div>
                  <div style={{ color: '#a21caf', fontWeight: 650, fontSize: 16, marginBottom: 6 }}>Số gói dịch vụ đã bán</div>
                  <div style={{ color: '#111827', fontWeight: 700, fontSize: 32 }}>{totalOrder.total || 0}</div>
                </div>
              </div>
            </Card>
          </Col>
          {/* Tổng đơn hàng */}
          <Col span={6} style={{ marginBottom: 20, display: 'flex', justifyContent: 'center' }}>
            <Card
              bordered={false}
              style={{
                width: 500,
                background: '#abf2cdff', // Xanh lá nhạt
                boxShadow: '0 4px 16px #6ee7b744',
                borderRadius: 18
              }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
                <div
                  style={{
                    background: '#10b981', // Green-500
                    borderRadius: 16,
                    width: 48,
                    height: 48,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                  <DollarCircleOutlined style={{ fontSize: 28, color: '#fff' }} />
                </div>
                <div>
                  <div style={{ color: '#10b981', fontWeight: 650, fontSize: 16, marginBottom: 6 }}>Tổng doanh thu</div>
                  <div style={{ color: '#111827', fontWeight: 700, fontSize: 32 }}>${Number(totalRevenue.total || 0).toFixed(2)}</div>
                </div>
              </div>
            </Card>
          </Col>
          {/* //Phí gas */}
          <Col span={6} style={{ marginBottom: 20, display: 'flex', justifyContent: 'center' }}>
            <Card
              bordered={false}
              style={{
                width: '100%',
                background: '#faedb7ff', // Cam nhạt, năng lượng/gas
                boxShadow: '0 4px 16px #fbbf2444',
                borderRadius: 18
              }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
                <div
                  style={{
                    background: '#f59e42', // Cam đậm
                    borderRadius: 16,
                    width: 48,
                    height: 48,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                  <ThunderboltOutlined style={{ fontSize: 28, color: '#fff' }} />
                </div>
                <div>
                  <div style={{ color: '#f59e42', fontWeight: 650, fontSize: 16, marginBottom: 6 }}>Tổng phí gas</div>
                  <div style={{ color: '#111827', fontWeight: 700, fontSize: 32 }}>{'Updating'}</div>
                </div>
              </div>
            </Card>
          </Col>

          {/* //BNB */}
          <Col span={12} style={{ marginBottom: 20, display: 'flex', justifyContent: 'center' }}>
            <Card
              bordered={false}
              style={{
                width: '100%',
                height: '100%',
                background: '#fffbe6', // Vàng nhạt pastel
                boxShadow: '0 4px 16px #ffe58f66',
                borderRadius: 18
              }}>
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between', // Đẩy hai bên
                  width: '100%',
                  gap: 16
                }}>
                {/* Bên trái: icon + estimatedGasFee */}
                <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
                  <div
                    style={{
                      background: '#f3ba2f',
                      borderRadius: 16,
                      width: 48,
                      height: 48,
                      padding: 20,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}>
                    <BSCCircleColorful style={{ fontSize: 32 }} />
                  </div>
                  <span style={{ color: '#cb9202ff', fontWeight: 700, fontSize: 16 }}>{'estimatedGasFee'}/BNB</span>
                </div>

                {/* Bên phải: link */}
                <a
                  href='https://bscscan.com/gastracker'
                  style={{
                    color: '#111827',
                    fontWeight: 700,
                    fontSize: 16,
                    textDecoration: 'underline',
                    whiteSpace: 'nowrap'
                  }}>
                  View On BSCScan
                </a>
              </div>
            </Card>
          </Col>

          {/*ETH */}
          <Col span={12} style={{ marginBottom: 20, display: 'flex', justifyContent: 'center' }}>
            <Card
              bordered={false}
              style={{
                width: '100%',
                height: '100%',
                background: '#e5e9fd', // Xanh tím nhạt Ethereum
                boxShadow: '0 4px 16px #b3b8ee44',
                borderRadius: 18
              }}>
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between', // Đẩy hai bên
                  gap: 16,
                  width: '100%'
                }}>
                {/* Bên trái: icon + giá trị */}
                <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
                  <div
                    style={{
                      background: '#627eea',
                      borderRadius: 16,
                      width: 48,
                      height: 48,
                      padding: 20,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}>
                    <EthereumCircleColorful style={{ fontSize: 32 }} />
                  </div>
                  <span style={{ color: '#2646c5ff', fontWeight: 650, fontSize: 16 }}>{'estimatedGasFee'}/BNB</span>
                </div>

                {/* Bên phải: link */}
                <a
                  href='https://etherscan.io/gastracker' // Thay link động nếu cần
                  style={{
                    color: '#111827',
                    fontWeight: 700,
                    fontSize: 16,
                    textDecoration: 'underline',
                    whiteSpace: 'nowrap'
                  }}>
                  View On Etherscan
                </a>
              </div>
            </Card>
          </Col>
        </Row>

        <Row gutter={24}>
          {/* Thống kê số lượng khách hàng */}
          <Col span={12} style={{ display: 'flex', justifyContent: 'center' }}>
            <CustomerDashBoardView />
          </Col>

          {/* Thống kê số lượng đơn hàng */}
          <Col span={12} style={{ display: 'flex', justifyContent: 'center' }}>
            <OrderDashBoardView />
          </Col>

          {/* Thống kê gói dịch vụ */}
          <Col span={24} style={{ marginBottom: 8, display: 'flex', justifyContent: 'center' }}>
            <PackageDashBoardView />
          </Col>

          {/* Thống kê số lượng giao dịch (Off-Chain) */}
          <Col span={24} style={{ marginBottom: 8, display: 'flex', justifyContent: 'center' }}>
            <PaymentDashBoardView />
          </Col>

          {/* Thống kê doanh thu */}
          <Col span={24} style={{ marginBottom: 8, display: 'flex', justifyContent: 'center' }}>
            <RevenueDashBoardView />
          </Col>

          {/* Thống kê doanh thu theo gói */}
          <Col span={24} style={{ marginBottom: 8, display: 'flex', justifyContent: 'center' }}>
            <RevenuePackageDashBoardView />
          </Col>

          {/* Thống kê số lượng giao dịch (On-Chain) */}
          <Col span={24} style={{ marginBottom: 8, display: 'flex', justifyContent: 'center' }}>
            <PaymentOnChainDashBoardView />
          </Col>
        </Row>
      </Spin>
    </BaseView>
  )
}

export default DashBoardView
