import { FC, useEffect, useState } from 'react'
import { Card, Col, DatePicker, Empty, Row, Select, Spin } from 'antd'
import { useDashBoard } from '../Hooks/useDashBoard'
import { Line, Pie } from 'react-chartjs-2'
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, ArcElement, PointElement, LineElement } from 'chart.js'
import dayjs from 'dayjs'
import BaseView from '~/components/BaseView'

type IProps = {}
ChartJS.register(CategoryScale, LinearScale, ArcElement, BarElement, CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend)

const RevenuePackageDashBoardView: FC<IProps> = () => {
  const [selectedYear, setSelectedYear] = useState(dayjs().year())
  const [selectedMonth, setSelectedMonth] = useState<number | undefined>(undefined)

  const { getRevenuePackageStats, revenuePackageStats, isLoading } = useDashBoard()

  const fetchStatsData = (year: number, month?: number) => {
    Promise.all([getRevenuePackageStats(year, month)]).catch((err) => {
      console.log('err', err)
    })
  }

  useEffect(() => {
    fetchStatsData(selectedYear, selectedMonth)
  }, [selectedYear, selectedMonth])

  return (
    <BaseView>
      <Spin spinning={isLoading}>
        <Row gutter={16}>
          <Col span={24} style={{ marginBottom: 8 }}>
            <b style={{ marginRight: 10 }}>Năm:</b>
            <DatePicker
              picker='year'
              defaultValue={dayjs()}
              style={{ width: 200, marginRight: 10 }}
              onChange={(value) => {
                if (!value) return
                const year = value.year()
                setSelectedYear(year)
                fetchStatsData(year, selectedMonth)
              }}
            />
            <b style={{ marginRight: 10 }}>Tháng:</b>
            <Select
              placeholder='Chọn tháng (tùy chọn)'
              style={{ width: 200 }}
              allowClear
              value={selectedMonth}
              onChange={(month) => {
                setSelectedMonth(month)
                fetchStatsData(selectedYear, month)
              }}
              options={[
                { value: 1, label: 'Tháng 1' },
                { value: 2, label: 'Tháng 2' },
                { value: 3, label: 'Tháng 3' },
                { value: 4, label: 'Tháng 4' },
                { value: 5, label: 'Tháng 5' },
                { value: 6, label: 'Tháng 6' },
                { value: 7, label: 'Tháng 7' },
                { value: 8, label: 'Tháng 8' },
                { value: 9, label: 'Tháng 9' },
                { value: 10, label: 'Tháng 10' },
                { value: 11, label: 'Tháng 11' },
                { value: 12, label: 'Tháng 12' }
              ]}
            />
          </Col>

          <Col span={24} style={{ marginBottom: 8, display: 'flex', justifyContent: 'center' }}>
            <Card style={{ width: '100%', height: '400px' }} title='Thống kê doanh thu theo gói'>
              <Row gutter={16} style={{ display: 'flex', justifyContent: 'center' }}>
                <Col span={12} style={{ width: 'auto', height: '300px', display: 'flex', justifyContent: 'center' }}>
                  {/* Pie Chart */}
                  {revenuePackageStats?.pieChart?.datasets?.length > 0 ? (
                    <Pie
                      data={{
                        labels: revenuePackageStats?.pieChart?.labels,
                        datasets: [...revenuePackageStats?.pieChart?.datasets]
                      }}
                      options={{
                        plugins: {
                          legend: {
                            display: true,
                            labels: {
                              font: {
                                size: 12,
                                weight: 'bold'
                              }
                            }
                          },
                          tooltip: {
                            callbacks: {
                              label: function (context) {
                                const value = context.raw
                                return value + ' %'
                              }
                            }
                          }
                        }
                      }}
                    />
                  ) : (
                    <Empty description='No Data' />
                  )}
                </Col>
                <Col span={12} style={{ width: 'auto', height: '300px', display: 'flex', justifyContent: 'center' }}>
                  {/* Line Chart */}
                  {revenuePackageStats?.lineChart?.datasets?.length > 0 ? (
                    <Line
                      data={{
                        labels: revenuePackageStats?.lineChart?.labels,
                        datasets: [...revenuePackageStats?.lineChart?.datasets]
                      }}
                      options={{
                        plugins: {
                          legend: {
                            display: true,
                            labels: {
                              font: {
                                size: 12,
                                weight: 'bold'
                              }
                            }
                          },
                          tooltip: {
                            callbacks: {
                              label: function (context) {
                                const value = context.raw
                                return value + ' USD'
                              }
                            }
                          }
                        },
                        scales: {
                          y: {
                            ticks: {
                              callback: function (value) {
                                return value.toLocaleString() + ' USD'
                              },
                              font: { size: 12 }
                            },
                            title: {
                              display: true,
                              text: 'Doanh thu',
                              font: {
                                size: 12
                              }
                            }
                          },
                          x: {
                            ticks: {
                              font: { size: 12 }
                            },
                            title: {
                              display: true,
                              text: 'Tháng',
                              font: {
                                size: 12
                              }
                            }
                          }
                        }
                      }}
                    />
                  ) : (
                    <Empty description='No Data' />
                  )}
                </Col>
              </Row>
            </Card>
          </Col>
        </Row>
      </Spin>
    </BaseView>
  )
}

export default RevenuePackageDashBoardView
