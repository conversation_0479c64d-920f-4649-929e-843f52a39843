import { FC, useEffect, useState } from 'react'
import { Card, Col, DatePicker, Empty, Row, Select, Spin } from 'antd'
import { useDashBoard } from '../Hooks/useDashBoard'
import { Line, Bar } from 'react-chartjs-2'
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, ArcElement, PointElement, LineElement } from 'chart.js'
import dayjs from 'dayjs'
import BaseView from '~/components/BaseView'

type IProps = {}
ChartJS.register(CategoryScale, LinearScale, ArcElement, BarElement, CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend)

const PaymentChainDashBoardView: FC<IProps> = () => {
  const [selectedYear, setSelectedYear] = useState(dayjs().year())
  const [selectedMonth, setSelectedMonth] = useState<number | undefined>(undefined)

  const { getOnChainStats, onChainStats, isLoading } = useDashBoard()

  const fetchStatsData = (year: number, month?: number) => {
    Promise.all([getOnChainStats(year, month)]).catch((err) => {
      console.log('err', err)
    })
  }

  useEffect(() => {
    fetchStatsData(selectedYear, selectedMonth)
  }, [selectedYear, selectedMonth])

  return (
    <BaseView>
    <Spin spinning={isLoading}>
        <Row gutter={16}>
          <Col span={24} style={{ marginBottom: 8 }}>
            <b style={{ marginRight: 10 }}>Năm:</b>
            <DatePicker
              picker='year'
              defaultValue={dayjs()}
              style={{ width: 200, marginRight: 10 }}
              onChange={(value) => {
                if (!value) return
                const year = value.year()
                setSelectedYear(year)
                fetchStatsData(year, selectedMonth)
              }}
            />
            <b style={{ marginRight: 10 }}>Tháng:</b>
            <Select
              placeholder="Chọn tháng (tùy chọn)"
              style={{ width: 200 }}
              allowClear
              value={selectedMonth}
              onChange={(month) => {
                setSelectedMonth(month)
                fetchStatsData(selectedYear, month)
              }}
              options={[
                { value: 1, label: 'Tháng 1' },
                { value: 2, label: 'Tháng 2' },
                { value: 3, label: 'Tháng 3' },
                { value: 4, label: 'Tháng 4' },
                { value: 5, label: 'Tháng 5' },
                { value: 6, label: 'Tháng 6' },
                { value: 7, label: 'Tháng 7' },
                { value: 8, label: 'Tháng 8' },
                { value: 9, label: 'Tháng 9' },
                { value: 10, label: 'Tháng 10' },
                { value: 11, label: 'Tháng 11' },
                { value: 12, label: 'Tháng 12' }
              ]}
            />
          </Col>

          <Col span={24} style={{ marginBottom: 8, display: 'flex', justifyContent: 'center' }}>
            <Card style={{ width: '100%', height: "400px" }} title='Thống kê số lượng giao dịch (On-Chain)'>
              <Row gutter={16} style={{ display: 'flex', justifyContent: 'center' }}>
                <Col span={12} style={{ height: '300px', display: 'flex', justifyContent: 'center' }}>
                  {onChainStats?.lineChart?.datasets?.length > 0 ? (
                    <Line
                      data={{
                        labels: onChainStats?.lineChart?.labels,
                        datasets: [
                          ...onChainStats?.lineChart?.datasets,
                        ]
                      }}
                      options={{
                        plugins: {
                          legend: {
                            display: true,
                            labels: {
                              font: {
                                size: 12,
                                weight: 'bold'
                              }
                            }
                          },
                          tooltip: {
                            callbacks: {
                              label: function (context) {
                                const value = context.raw
                                return value + ' Giao dịch (On-Chain)'
                              }
                            }
                          }
                        },
                        scales: {
                          x: {
                            ticks: {
                              font: {
                                size: 12
                              }
                            },
                          },
                          y: {
                            ticks: {
                              stepSize: 1,
                              callback: (value: any) => `${value}`,
                              font: {
                                size: 12
                              }
                            },
                            title: {
                              display: true,
                              text: 'Số lượng',
                              font: {
                                size: 12
                              }
                            }
                          }
                        }
                      }}
                    />
                  ) : (
                    <Empty description='No Data' />
                  )}
                </Col>
                <Col span={12} style={{ width: 'auto', height: '300px', display: 'flex', justifyContent: 'center' }}>
                  {onChainStats?.barChart?.datasets?.length > 0 ? (
                    <Bar
                      data={{
                        labels: onChainStats?.barChart?.labels,
                        datasets: [
                          ...onChainStats?.barChart?.datasets,
                        ]
                      }}
                      options={{
                        plugins: {
                          legend: {
                            display: true,
                            labels: {
                              font: {
                                size: 12,
                                weight: 'bold'
                              }
                            }
                          },
                          tooltip: {
                            callbacks: {
                              label: function (context) {
                                const value = context.raw
                                return value + ' Giao dịch (On-Chain)'
                              }
                            }
                          }
                        },
                        scales: {
                          x: {
                            ticks: {
                              font: {
                                size: 12
                              }
                            },
                          },
                          y: {
                            ticks: {
                              stepSize: 1,
                              callback: (value: any) => `${value}`,
                              font: {
                                size: 12
                              }
                            },
                            title: {
                              display: true,
                              text: 'Fees',
                              font: {
                                size: 12
                              }
                            }
                          }
                        }
                      }}
                     />
                  ) : (
                    <Empty description='No Data' />
                  )}
                </Col>
              </Row>
            </Card>
          </Col>
        </Row>
      </Spin></BaseView>
      
  )
}

export default PaymentChainDashBoardView
