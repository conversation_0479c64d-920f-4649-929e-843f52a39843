import { useInfiniteQuery, useMutation } from '@tanstack/react-query'
import { useEffect, useState } from 'react'
import { IMemberFilter } from '~/dto/member.dto'
import { toastService } from '~/services'
import { configPackageService } from '~/services/ConfigPackageService/configPackageService'
import { memberService } from '~/services/MemberService/memberService'

export const useMember = (enabled: boolean = false) => {
  const [filter, setFilter] = useState<IMemberFilter>({})
  const [isLoading, setIsLoading] = useState(false)
  const { data, refetch, isFetching } = useInfiniteQuery({
    queryKey: [memberService.MEMBER.LIST, filter],
    queryFn: async () => {
      return await memberService.getMemberList(filter)
    },
    getNextPageParam: (lastPage, allPages) => {
      if (lastPage.data.length === 0) return undefined
      return allPages.length + 1
    },
    initialPageParam: 1,
    enabled: enabled
  })

  useEffect(() => {
    setIsLoading(isFetching)
  }, [isFetching])

  const getMemberPackages = async (body: { id: string}) => {
    setIsLoading(true)
    let data = await memberService.getMemberPackages(body)
    setIsLoading(false)
    return data
  }

  const getMemberOrders = async (body: { id: string; pageSize?: number; pageIndex?: number }) => {
    setIsLoading(true)
    let data = await memberService.getMemberOrders(body)
    setIsLoading(false)
    return data
  }

  const getMemberConfigPackages = async (body: { id: string; pageSize?: number; pageIndex?: number }) => {
    setIsLoading(true)
    let data = await memberService.getMemberConfigPackages(body)
    setIsLoading(false)
    return data
  }

  const getMemberKey = async (body: { id: string }) => {
    setIsLoading(true)
    let data = await memberService.getMemberKey(body)
    setIsLoading(false)
    return data
  }

  const getMemberConfigPackageDetails = async (id: string) => {
    setIsLoading(true)
    let data = await configPackageService.getConfigPackageDetails(id)
    setIsLoading(false)
    return data
  }

  const getMemberConfigLogs = async (body: { configId: string; pageSize?: number; pageIndex?: number }) => {
    setIsLoading(true)
    let data = await configPackageService.getConfigPackageLogs(body)
    setIsLoading(false)
    return data
  }

  const useBlockMember = () => {
    return useMutation({
      mutationFn: async (id: string) => {
        return await memberService.blockMember(id)
      },
      onSuccess: () => {
        toastService.success('Khóa thành công')
        refetch()
      },
      onError: (error) => {
        console.log(error)
      }
    })
  }

  const useUnblockMember = () => {
    return useMutation({
      mutationFn: async (id: string) => {
        return await memberService.unblockMember(id)
      },
      onSuccess: () => {
        toastService.success('Mở khóa thành công')
        refetch()
      },
      onError: (error) => {
        console.log(error)
      }
    })
  }

  // member project
  const getMemberProjects = async (body: { id: string; pageSize?: number; pageIndex?: number }) => {
    setIsLoading(true)
    let data = await memberService.getMemberProjects(body)
    setIsLoading(false)
    return data
  }

  // member payment transaction
  const getMemberPaymentTransactions = async (body: { id: string; pageSize?: number; pageIndex?: number }) => {
    setIsLoading(true)
    let data = await memberService.getMemberPaymentTransactions(body)
    setIsLoading(false)
    return data
  }

  const formattedData = data?.pages?.flatMap((page) => page.data)
  const total = data?.pages?.[0]?.total
  return {
    data: formattedData,
    total: total,
    isLoading,
    isError: false,
    loadMemberList: refetch,
    setFilter,
    useBlockMember,
    useUnblockMember,
    getMemberPackages,
    getMemberOrders,
    getMemberConfigPackages,
    getMemberConfigLogs,
    getMemberConfigPackageDetails,
    getMemberKey,
    getMemberProjects,
    getMemberPaymentTransactions
  }
}
