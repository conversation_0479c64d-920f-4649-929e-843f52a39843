import { useState } from 'react'
import { enumData } from '~/common/enums/enumData'
import { IFilter } from '~/components/BaseFilter/BaseFilter'

interface IProps {}
export const useMemberFilterConfig = (iProps?: IProps) => {
  const handleFilter = (values: any) => {
    setFilterData(values)
  }
  const handleFilterReset = () => {
    setFilterData({})
  }

  const [filterData, setFilterData] = useState({})
  const [filterFields, setFilterFields] = useState<IFilter[]>([
    //tên khách hang
    {
      key: 'fullName',
      name: 'Tên khách hàng',
      type: enumData.FILTER_TYPE.INPUT.key
    },

    //trạng thái
    {
      key: 'status',
      name: 'Trạng thái hoạt động',
      type: enumData.FILTER_TYPE.SELECT.key,
      selectOptions: Object.values(enumData.MEMBER_ACTIVE_STATUS).map((status: any) => {
        return {
          key: status.key,
          value: status.value
        }
      })
    },

    //trạng thái xác thực
    {
      key: 'statusValidate',
      name: 'Trạng thái xác thực',
      type: enumData.FILTER_TYPE.SELECT.key,
      selectOptions: Object.values(enumData.MEMBER_VALIDATE_STATUS).map((status: any) => {
        return {
          key: status.key,
          value: status.value
        }
      })
    }
  ])

  return {
    filterFields,
    filterData,
    setFilterData,
    handleFilter,
    handleFilterReset
  }
}
