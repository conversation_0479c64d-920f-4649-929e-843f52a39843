import { Card, Tag } from 'antd'
import { ColumnsType } from 'antd/es/table'
import moment from 'moment'
import { useEffect, useState } from 'react'
import { enumData } from '~/common/enums/enumData'
import BaseTable from '~/components/BaseTable'
import { IOrder, IOrderRespone } from '~/dto/order.dto'
import { useMember } from '../../Hooks/useMember'

interface IProps {
  memberId: string
  open: boolean
}

export const MemberOrder = ({ memberId, open = false }: IProps) => {
  const { getMemberOrders, isLoading } = useMember()

  const [orders, setOrders] = useState<IOrderRespone>()
  const [currentPageIndex, setCurrentPageIndex] = useState(1)
  const [currentPageSize, setCurrentPageSize] = useState(10)
  useEffect(() => {
    if (open) {
      loadData(currentPageIndex, currentPageSize)
    }
  }, [open])
  const loadData = (pageIndex?: number, pageSize?: number) => {
    //api
    setCurrentPageIndex(pageIndex || 1)
    setCurrentPageSize(pageSize || 10)
    getMemberOrders({ id: memberId, pageIndex, pageSize }).then((res) => {
      setOrders(res)
    })
  }
  const orderColumns: ColumnsType<IOrder> = [
    {
      title: 'STT',
      align: 'center',
      dataIndex: 'index',
      key: 'index',
      width: 50,
      render: (text, record, index) => index + 1
    },
    {
      title: 'Mã đơn hàng',
      align: 'center',
      dataIndex: 'code',
      key: 'code'
    },

    //total
    {
      title: 'Tổng tiền thanh toán',
      align: 'center',
      dataIndex: 'totalPrice',
      key: 'totalPrice',
      render: (value) => '$' + value
    },
    //ngày mua
    {
      title: 'Ngày đặt hàng',
      align: 'center',
      dataIndex: 'createdDate',
      key: 'createdDate',
      render: (value) => (value ? moment(value).format('DD/MM/YYYY') : '')
    },

    {
      title: 'Ngày thanh toán',
      align: 'center',
      dataIndex: 'paymentDate',
      key: 'paymentDate',
      render: (value) => (value ? moment(value).format('DD/MM/YYYY') : '')
    },
    {
      title: 'Trạng thái',
      align: 'center',
      dataIndex: 'paymentStatus',
      key: 'paymentStatus',
      render: (value) => <Tag color={enumData.ORDER_PAYMENT_STATUS[value]?.color}>{enumData.ORDER_PAYMENT_STATUS[value]?.value}</Tag>
    }
  ]
  return (
    <Card>
      <BaseTable data={orders?.data} columns={orderColumns} total={orders?.total} isLoading={isLoading} onPageChange={loadData} />
    </Card>
  )
}
