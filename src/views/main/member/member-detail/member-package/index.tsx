import { Card, Col, Row, Tag, Typography, Divider } from 'antd'
import moment from 'moment'
import { enumData } from '~/common/enums/enumData'
import BaseView from '~/components/BaseView'

const { Text } = Typography

interface IPackageData {
  id: string
  createdDate: string
  updatedDate: string
  createdBy: string | null
  updatedBy: string | null
  memberId: string
  packagePlanId: string
  expiredDate: string
  activatedDate: string
  nextPaymentDate: string
  initialTransactionLimit: number
  currentTransaction: number
  initialConfigLimit: number
  currentConfig: number
  projectLimit: number
  currentProject: number
  status: string
  isAutoRenewal: boolean
  subscriptionId: string
  subscriptionProvider: string
  isTrial: boolean
  buyPrice: string
  timeRegister: number
  byteLimit: number
}

interface IPackagePlans {
  id: string
  createdDate: string
  updatedDate: string
  createdBy: string | null
  updatedBy: string | null
  code: string
  name: string
  originalPrice: string
  note: string
  isMostPopular: boolean
  status: string
  displayOrder: number
  isHidden: boolean
}

interface IMemberPackageData {
  data: IPackageData
  packagePlans: IPackagePlans
}

interface IProps {
  data?: IMemberPackageData | any
}

export const MemberPackage = ({ data }: IProps) => {
  if (!data) {
    return (
      <Card>
        <div style={{ textAlign: 'center', color: '#999', padding: '20px' }}>Không có thông tin gói dịch vụ</div>
      </Card>
    )
  }

  // Handle both the expected API format and the actual format
  let packageData: IPackageData
  let packagePlans: IPackagePlans

  if (data.data && data.packagePlans) {
    // Direct format from your JSON
    packageData = data.data
    packagePlans = data.packagePlans
  } else if (data.data && Array.isArray(data.data) && data.data.length > 0) {
    // Expected API format with array
    const firstPackage = data.data[0]
    packageData = firstPackage
    packagePlans = firstPackage.packageDetails || {}
  } else {
    return (
      <Card>
        <div style={{ textAlign: 'center', color: '#999', padding: '20px' }}>Không có thông tin gói dịch vụ</div>
      </Card>
    )
  }

  return (
    <BaseView>
      <Row gutter={24}>
        {/* Package Plan Information */}
        <Col span={12}>
          <Row style={{ marginBottom: 12 }}>
            <Col span={8}>
              <Text strong>Tên gói:</Text>
            </Col>
            <Col span={16}>
              <Text>{packagePlans?.name || 'N/A'}</Text>
            </Col>
          </Row>

          <Row style={{ marginBottom: 12 }}>
            <Col span={8}>
              <Text strong>Giá mua:</Text>
            </Col>
            <Col span={16}>
              <Text>${packageData?.buyPrice || '0'}</Text>
            </Col>
          </Row>

          <Row style={{ marginBottom: 12 }}>
            <Col span={8}>
              <Text strong>Trạng thái gói:</Text>
            </Col>
            <Col span={16}>
              <Tag color={enumData.PACKAGE_STATUS[packagePlans?.status]?.color || 'default'}>
                {enumData.PACKAGE_STATUS[packagePlans?.status]?.value || packagePlans?.status || 'N/A'}
              </Tag>
            </Col>
          </Row>
          <Row style={{ marginBottom: 12 }}>
            <Col span={10}>
              <Text strong>Tự động gia hạn:</Text>
            </Col>
            <Col span={14}>
              <Tag color={packageData?.isAutoRenewal ? 'green' : 'red'}>{packageData?.isAutoRenewal ? 'Có' : 'Không'}</Tag>
            </Col>
          </Row>
        </Col>

        {/* Subscription Information */}
        <Col span={12}>
          <Row style={{ marginBottom: 12 }}>
            <Col span={10}>
              <Text strong>Trạng thái:</Text>
            </Col>
            <Col span={14}>
              <Tag color={enumData.MEMBER_PACKAGE_STATUS[packageData?.status]?.color || 'default'}>
                {enumData.MEMBER_PACKAGE_STATUS[packageData?.status]?.value || packageData?.status || 'N/A'}
              </Tag>
            </Col>
          </Row>

          <Row style={{ marginBottom: 12 }}>
            <Col span={10}>
              <Text strong>Ngày kích hoạt:</Text>
            </Col>
            <Col span={14}>
              <Text>{packageData?.activatedDate ? moment(packageData.activatedDate).format('DD/MM/YYYY HH:mm') : 'N/A'}</Text>
            </Col>
          </Row>

          <Row style={{ marginBottom: 12 }}>
            <Col span={10}>
              <Text strong>Ngày hết hạn:</Text>
            </Col>
            <Col span={14}>
              <Text>{packageData?.expiredDate ? moment(packageData.expiredDate).format('DD/MM/YYYY HH:mm') : 'N/A'}</Text>
            </Col>
          </Row>

          <Row style={{ marginBottom: 12 }}>
            <Col span={10}>
              <Text strong>Thanh toán tiếp theo:</Text>
            </Col>
            <Col span={14}>
              <Text>{packageData?.nextPaymentDate ? moment(packageData.nextPaymentDate).format('DD/MM/YYYY HH:mm') : 'N/A'}</Text>
            </Col>
          </Row>
        </Col>
      </Row>
      <Divider />
      {/* Usage Statistics */}
      <div style={{ marginBottom: 16 }}>
        <Text strong style={{ fontSize: 16, color: '#fa8c16' }}>
          Thống kê sử dụng
        </Text>
      </div>

      <Row gutter={24}>
        <Col span={8}>
          <Card size='small' style={{ textAlign: 'center' }}>
            <div style={{ marginBottom: 8 }}>
              <Text strong>Giao dịch</Text>
            </div>
            <div style={{ fontSize: 24, fontWeight: 'bold', color: '#1890ff' }}>
              {packageData?.currentTransaction || 0}/{packageData?.initialTransactionLimit || 0}
            </div>
            <div style={{ marginTop: 4 }}>
              <Text type='secondary' style={{ fontSize: 12 }}>
                Đã sử dụng:{' '}
                {packageData?.initialTransactionLimit
                  ? (((packageData?.currentTransaction || 0) / packageData.initialTransactionLimit) * 100).toFixed(1)
                  : 0}
                %
              </Text>
            </div>
          </Card>
        </Col>

        <Col span={8}>
          <Card size='small' style={{ textAlign: 'center' }}>
            <div style={{ marginBottom: 8 }}>
              <Text strong>Cấu hình</Text>
            </div>
            <div style={{ fontSize: 24, fontWeight: 'bold', color: '#52c41a' }}>
              {packageData?.currentConfig || 0}/{packageData?.initialConfigLimit || 0}
            </div>
            <div style={{ marginTop: 4 }}>
              <Text type='secondary' style={{ fontSize: 12 }}>
                Đã sử dụng:{' '}
                {packageData?.initialConfigLimit ? (((packageData?.currentConfig || 0) / packageData.initialConfigLimit) * 100).toFixed(1) : 0}%
              </Text>
            </div>
          </Card>
        </Col>

        <Col span={8}>
          <Card size='small' style={{ textAlign: 'center' }}>
            <div style={{ marginBottom: 8 }}>
              <Text strong>Dự án</Text>
            </div>
            <div style={{ fontSize: 24, fontWeight: 'bold', color: '#fa541c' }}>
              {packageData?.currentProject || 0}/{packageData?.projectLimit || 0}
            </div>
            <div style={{ marginTop: 4 }}>
              <Text type='secondary' style={{ fontSize: 12 }}>
                Đã sử dụng: {packageData?.projectLimit ? (((packageData?.currentProject || 0) / packageData.projectLimit) * 100).toFixed(1) : 0}%
              </Text>
            </div>
          </Card>
        </Col>
      </Row>

      <Row gutter={24} style={{ marginTop: 16 }}>
        <Col span={12}>
          <Card size='small' style={{ textAlign: 'center' }}>
            <div style={{ marginBottom: 8 }}>
              <Text strong>Giới hạn dung lượng</Text>
            </div>
            <div style={{ fontSize: 20, fontWeight: 'bold', color: '#722ed1' }}>{packageData?.byteLimit || 0} bytes</div>
          </Card>
        </Col>

        <Col span={12}>
          <Card size='small' style={{ textAlign: 'center' }}>
            <div style={{ marginBottom: 8 }}>
              <Text strong>Số lần đăng ký</Text>
            </div>
            <div style={{ fontSize: 20, fontWeight: 'bold', color: '#eb2f96' }}>{packageData?.timeRegister || 0}</div>
          </Card>
        </Col>
      </Row>
    </BaseView>
  )
}
