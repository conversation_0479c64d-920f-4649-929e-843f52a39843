import { ApiOutlined, DatabaseOutlined, CodeOutlined, InfoCircleFilled, CopyOutlined } from '@ant-design/icons'
import { Button, Card, Col, Row, Spin, Tabs, Tag, Typography } from 'antd'
import { ColumnsType } from 'antd/es/table'
import moment from 'moment'
import { enumData } from '~/common/enums/enumData'
import BaseTable from '~/components/BaseTable'
import { ILogs, IMemberConfigPackage, IMemberConfigPackageResponse } from '~/dto/subscriptionPlan.dto'
import { useMember } from '../../Hooks/useMember'
import { useEffect, useState } from 'react'

interface IProps {
  memberId: string
  open: boolean
}
const { Text, Paragraph } = Typography

export const MemberKey = ({ memberId, open = false }: IProps) => {
  const { getMemberKey, isLoading } = useMember()
  const [memberKey, setMemberKey] = useState<any>()

  //Lưu lại pageIndex, pageSize hiện tại để không bị reset khi chuyển tab
  const [currentPageIndex, setCurrentPageIndex] = useState(1)
  const [currentPageSize, setCurrentPageSize] = useState(10)

  useEffect(() => {
    if (!open) return
    loadData(currentPageIndex, currentPageSize)
  }, [open])

  //Lấy danh sách gói cấu hình (găn key cho từng cấu hình để fix lỗi EXPAND table ANTD)
  const loadData = async (pageIndex?: number, pageSize?: number) => {
    getMemberKey({ id: memberId }).then((res) => {
      setMemberKey(res)
    })
  }

  return (
    <Spin spinning={isLoading}>
      <Card>
        <Row>
          <Col span={24} style={{ padding: 10 }}>
            <span style={{ fontWeight: 'bold', fontSize: 20 }}>Public key: </span>
            <div
              style={{
                fontSize: 16,
                backgroundColor: '#f5f5f5',
                padding: 10,
                borderRadius: 6,
                borderColor: '#ccc',
                borderWidth: 1,
                borderStyle: 'solid',
                marginTop: 15
              }}>
              -----BEGIN PUBLIC KEY-----
              <br />
              {memberKey?.publicKey}
              <br />
              -----END PUBLIC KEY-----
            </div>
          </Col>

          <Col span={24} style={{ padding: 10 }}>
            <span style={{ fontWeight: 'bold', fontSize: 20 }}>API key: </span>
            <div
              style={{
                fontSize: 16,
                backgroundColor: '#f5f5f5',
                padding: 10,
                borderRadius: 6,
                borderColor: '#ccc',
                borderWidth: 1,
                borderStyle: 'solid',
                marginTop: 15
              }}>
              {memberKey?.apiKey}
            </div>
          </Col>
        </Row>
      </Card>
    </Spin>
  )
}
