import { Card, Tag } from 'antd'
import { ColumnsType } from 'antd/es/table'
import moment from 'moment'
import { useEffect, useState } from 'react'
import { enumData } from '~/common/enums/enumData'
import BaseTable from '~/components/BaseTable'
import { useMember } from '../../Hooks/useMember'

// Project interfaces
export interface IMemberProject {
  id: string
  createdDate: string
  updatedDate: string
  projectName: string
  projectDescription: string
  projectCode: string
  status: string
}

export interface IMemberProjectResponse {
  data: IMemberProject[]
  total: number
}

interface IProps {
  memberId: string
  open: boolean
}

export const MemberProject = ({ memberId, open = false }: IProps) => {
  const { getMemberProjects, isLoading } = useMember()
  const [projects, setProjects] = useState<IMemberProjectResponse>()
  const [currentPageIndex, setCurrentPageIndex] = useState(1)
  const [currentPageSize, setCurrentPageSize] = useState(10)

  useEffect(() => {
    if (open) {
      loadData(currentPageIndex, currentPageSize)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open, memberId])

  const loadData = (pageIndex?: number, pageSize?: number) => {
    setCurrentPageIndex(pageIndex || 1)
    setCurrentPageSize(pageSize || 10)
    getMemberProjects({ id: memberId, pageIndex, pageSize }).then((res) => {
      setProjects(res)
    })
  }

  const projectColumns: ColumnsType<IMemberProject> = [
    {
      title: 'STT',
      align: 'center',
      dataIndex: 'index',
      key: 'index',
      width: 60,
      render: (_, __, index) => index + 1
    },
    {
      title: 'Mã dự án',
      align: 'center',
      dataIndex: 'projectCode',
      key: 'projectCode',
      width: 150
    },
    {
      title: 'Tên dự án',
      align: 'left',
      dataIndex: 'projectName',
      key: 'projectName',
      width: 200
    },
    {
      title: 'Mô tả',
      align: 'left',
      dataIndex: 'projectDescription',
      key: 'projectDescription',
      width: 300,
      render: (value) => (
        <div style={{
          maxWidth: 280,
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap'
        }}>
          {value || 'Không có mô tả'}
        </div>
      )
    },
    {
      title: 'Ngày tạo',
      align: 'center',
      dataIndex: 'createdDate',
      key: 'createdDate',
      width: 150,
      render: (value) => (value ? moment(value).format('DD/MM/YYYY HH:mm') : '')
    },
    {
      title: 'Ngày cập nhật',
      align: 'center',
      dataIndex: 'updatedDate',
      key: 'updatedDate',
      width: 150,
      render: (value) => (value ? moment(value).format('DD/MM/YYYY HH:mm') : '')
    },
    {
      title: 'Trạng thái',
      align: 'center',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (value) => (
        <Tag color={enumData.PACKAGE_STATUS[value]?.color || 'default'}>
          {enumData.PACKAGE_STATUS[value]?.value || value}
        </Tag>
      )
    }
  ]

  return (
    <Card>
      <BaseTable
        data={projects?.data}
        columns={projectColumns}
        total={projects?.total}
        isLoading={isLoading}
        onPageChange={loadData}
        scroll={{ x: 'max-content' }}
      />
    </Card>
  )
}

export default MemberProject
