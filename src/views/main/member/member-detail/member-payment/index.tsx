import { Tag, Typography } from 'antd'
import { ColumnsType } from 'antd/es/table'
import moment from 'moment'
import { enumData } from '~/common/enums/enumData'
import BaseTable from '~/components/BaseTable'
import { useCallback, useEffect, useState } from 'react'
import { useMember } from '../../Hooks/useMember'

interface IProps {
  memberId: string
  open: boolean
}

// Interface for Member Payment Transaction data
export interface IMemberPaymentTransaction {
  id: string
  orderId: string
  paymentProvider: 'STRIPE' | 'PAYPAL' | 'BANK_TRANSFER' | string
  transactionType: 'DOWNGRADE' | 'UPGRADE' | 'PAYMENT' | 'REFUND' | string
  status: 'COMPLETED' | 'PENDING' | 'FAILED' | 'CANCELLED' | string
  amount: string
  grossAmount: string
  paymentMethod: 'BANK_TRANSFER' | 'CREDIT_CARD' | 'DEBIT_CARD' | 'PAYPAL' | string
  transactionDate: string
}

// Interface for API response
export interface IMemberPaymentTransactionResponse {
  data: IMemberPaymentTransaction[]
  total: number
}

const { Text } = Typography

export const MemberPaymentTransaction = ({ memberId, open = false }: IProps) => {
  const { getMemberPaymentTransactions, isLoading } = useMember()
  const [data, setData] = useState<IMemberPaymentTransaction[]>([])
  const [total, setTotal] = useState(0)
  const [pageIndex, setPageIndex] = useState(1)
  const [pageSize, setPageSize] = useState(10)

  // Load payment transactions data
  const loadData = useCallback(async () => {
    if (!memberId) return

    try {
      const response = await getMemberPaymentTransactions({
        id: memberId,
        pageIndex,
        pageSize
      })

      setData(response.data || [])
      setTotal(response.total || 0)
    } catch (error) {
      console.error('Error loading payment transactions:', error)
      setData([])
      setTotal(0)
    }
  }, [memberId, pageIndex, pageSize, getMemberPaymentTransactions])

  // Define table columns
  const columns: ColumnsType<IMemberPaymentTransaction> = [
    {
      title: 'ID Giao dịch',
      dataIndex: 'id',
      key: 'id',
      align: 'center',
      width: 200,
      render: (value: string) => (
        <Text copyable={{ text: value }} style={{ fontSize: '12px' }}>
          {value.substring(0, 8)}...
        </Text>
      )
    },
    {
      title: 'ID Đơn hàng',
      dataIndex: 'orderId',
      key: 'orderId',
      align: 'center',
      width: 200,
      render: (value: string) => (
        <Text copyable={{ text: value }} style={{ fontSize: '12px' }}>
          {value.substring(0, 8)}...
        </Text>
      )
    },
    {
      title: 'Nhà cung cấp',
      dataIndex: 'paymentProvider',
      key: 'paymentProvider',
      align: 'center',
      width: 120,
      render: (value: string) => (
        <Tag color={enumData.PAYMENT_PROVIDER[value]?.color || 'blue'}>{enumData.PAYMENT_PROVIDER[value]?.value || value}</Tag>
      )
    },
    {
      title: 'Loại giao dịch',
      dataIndex: 'transactionType',
      key: 'transactionType',
      align: 'center',
      width: 120,
      render: (value: string) => (
        <Tag color={enumData.TRANSACTION_TYPE[value]?.color || 'default'}>{enumData.TRANSACTION_TYPE[value]?.value || value}</Tag>
      )
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      align: 'center',
      width: 120,
      render: (value: string) => (
        <Tag color={enumData.TRANSACTION_STATUS[value]?.color || 'default'}>{enumData.TRANSACTION_STATUS[value]?.value || value}</Tag>
      )
    },
    {
      title: 'Tổng tiền',
      dataIndex: 'amount',
      key: 'amount',
      align: 'center',
      width: 100,
      render: (value: string) => `$${value}`
    },
    {
      title: 'Thời gian',
      dataIndex: 'transactionDate',
      key: 'transactionDate',
      align: 'center',
      width: 180,
      render: (value: string) => moment(value).format('DD/MM/YYYY HH:mm')
    }
  ]

  const handlePageChange = (newPageIndex: number, newPageSize: number) => {
    setPageIndex(newPageIndex)
    if (newPageSize !== pageSize) {
      setPageSize(newPageSize)
    }
  }

  if (!open) {
    return null
  }

  useEffect(() => {
    if (open) {
      loadData()
    }
  }, [open, memberId, loadData])

  return (
    <div style={{ padding: '16px' }}>
      <BaseTable columns={columns} data={data} total={total} isLoading={isLoading} onPageChange={handlePageChange} scroll={{ x: 'max-content' }} />
    </div>
  )
}
