import { <PERSON>, <PERSON>, Divider, <PERSON>, <PERSON>, Tabs, Tag } from 'antd'
import { IMember } from '~/dto/member.dto'
import { enumData } from '~/common/enums/enumData'
import { useEffect, useState } from 'react'
import { MemberOrder } from './member-order'
import { MemberPackage } from './member-package'
import { useMember } from '../Hooks/useMember'
import { IMemberSubscriptionPlanResponse } from '~/dto/subscriptionPlan.dto'
import { MemberProject} from './member-project'
import { MemberPaymentTransaction } from './member-payment'

export const MemberDetail = ({ data }: { data: IMember }) => {
  const { getMemberPackages, isLoading } = useMember()
  const [packages, setPackages] = useState<IMemberSubscriptionPlanResponse>()
  const [currentTab, setCurrentTab] = useState('1')

  const loadData = () => {
    getMemberPackages({ id: data.id }).then((res) => {
      setPackages(res)
    })
  }

  const onTabChange = (key: string) => {
    setCurrentTab(key)
  }

  useEffect(() => {
    loadData()
  }, [])

  const MemberDetail = () => {
    return (
      <Card>
        <Row>
          <Col span={12}>
            <Col span={24} style={{ margin: 14 }}>
              <span style={{ fontWeight: 'bold', fontSize: 14 }}>Họ Tên: </span>
              <span style={{ fontSize: 12 }}>{data.fullName}</span>
            </Col>

            <Col span={24} style={{ margin: 14 }}>
              <span style={{ fontWeight: 'bold', fontSize: 14 }}>Email: </span>
              <span style={{ fontSize: 12 }}>{data.email}</span>
            </Col>

            {/* Phone */}
            <Col span={24} style={{ margin: 14 }}>
              <span style={{ fontWeight: 'bold', fontSize: 14 }}>Số điện thoại: </span>
              <span style={{ fontSize: 12 }}>{data.phone}</span>
            </Col>

            <Col span={24} style={{ margin: 14 }}>
              <span style={{ fontWeight: 'bold', fontSize: 14 }}>Trạng thái: </span>
              <Tag style={{ fontSize: 12, padding: 4 }} color={enumData.MEMBER_ACTIVE_STATUS[data.status]?.color}>
                {enumData.MEMBER_ACTIVE_STATUS[data.status]?.value}
              </Tag>
            </Col>
          </Col>

          <Col span={12}>
            <Col span={24} style={{ margin: 10 }}>
              <div style={{ fontWeight: 'bold', fontSize: 20 }}>Ảnh đại diện: </div>
              {data.avatar && <img src={data.avatar} alt='avatar' style={{ width: 100, height: 100 }} />}
            </Col>
          </Col>
        </Row>
        {/* Thông tin packages */}
        <Divider/>
        <Row>
          <Col span={24}>
            {
              isLoading 
              ? <Spin/>
              : (
                <MemberPackage data={packages} />
              )
            }
          </Col>
        </Row>
      </Card>
    )
  }

  return (
    <div style={{ width: '100%' }}>
      <Tabs defaultActiveKey='1' onChange={(key) => onTabChange(key)}>
        <Tabs.TabPane tab='Thông tin khách hàng' key='1'>
          <MemberDetail />
        </Tabs.TabPane>

        <Tabs.TabPane tab='Dự án' key='2'>
          <MemberProject memberId={data.id} open={currentTab === '2'} />
        </Tabs.TabPane>

        <Tabs.TabPane tab='Giao dịch (Off Chain)' key='3'>
          <MemberPaymentTransaction memberId={data.id} open={currentTab === '3'} />
        </Tabs.TabPane>

        <Tabs.TabPane tab='Giao dịch (On Chain)' key='4'>
         
        </Tabs.TabPane>
      </Tabs>
    </div>
  )
}
