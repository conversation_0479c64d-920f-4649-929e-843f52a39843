import { EditOutlined, SaveOutlined } from '@ant-design/icons'
import { Row, Col, Form, Input, Button, Select, message } from 'antd'
import BaseButton from '~/components/BaseButton'
import BaseText from '~/components/BaseText'
import BaseModal from '~/components/BaseModal'
import { useEffect } from 'react'
import { useForm } from 'antd/es/form/Form'
import dayjs from 'dayjs'
import { useTranslation } from 'react-i18next'
import { IMember } from '~/dto/member.dto'
import { useModal } from '~/views/global-hooks/useModal'

const { Option } = Select

interface EditButtonProps {
  data: IMember
  onSuccess?: () => void
}

const EditButton = ({ data, onSuccess }: EditButtonProps) => {
  const { open, openModal, closeModal } = useModal()
  const [form] = useForm()

  useEffect(() => {
    if (open && data) {
      form.setFieldsValue({
        ...data
      })
    }
  }, [open, data, form])

  if (!data) return null

  const handleSave = async (values: any) => {
    if (!data) return
    const body = {
      ...values,
      startDate: values.startDate?.toISOString(),
      endDate: values.endDate?.toISOString()
    }

    try {
      // TODO: Implement update loyalty mutation
      // await updateLoyalty(body)
      closeModal()
      onSuccess && onSuccess()
      form.resetFields()
    } catch (error) {
      message.error(error.message)
    }
  }

  const modalContent = (
    <div>
      <Form form={form} layout='vertical' onFinish={handleSave}>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label='Họ và tên' name='fullName' rules={[{ required: true, message: 'Vui lòng nhập họ và tên' }]}>
              <Input />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label='Email' name='email' rules={[{ required: true, message: 'Vui lòng nhập email' }]}>
              <Input />
            </Form.Item>
          </Col>
        </Row>
        <div
          style={{
            textAlign: 'right',
            marginTop: 24,
            borderTop: '1px solid #f0f0f0',
            paddingTop: 16
          }}>
          <Button onClick={closeModal} style={{ marginRight: 8 }}>
            Hủy
          </Button>
          <Button type='primary' htmlType='submit' icon={<SaveOutlined />}>
            Cập nhật
          </Button>
        </div>
      </Form>
    </div>
  )

  return (
    <>
      <BaseButton icon={<EditOutlined />} onClick={openModal} type='primary' tooltip='Chỉnh sửa' />
      <BaseModal
        open={open}
        onClose={closeModal}
        title={'Chỉnh sửa thông tin khách hàng'}
        description={'Chỉnh sửa thông tin khách hàng'}
        childrenBody={modalContent}
      />
    </>
  )
}

export default EditButton
