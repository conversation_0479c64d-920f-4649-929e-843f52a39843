import { EyeOutlined } from '@ant-design/icons'
import BaseButton from '~/components/BaseButton'
import BaseModal from '~/components/BaseModal'
import { IMember } from '~/dto/member.dto'
import { useModal } from '~/views/global-hooks/useModal'
import { MemberDetail } from '../../member-detail'

interface DetailButtonProps {
  data: IMember
}

const DetailButton = ({ data }: DetailButtonProps) => {
  const { open, openModal, closeModal } = useModal()

  if (!data) return null

  return (
    <>
      <BaseButton icon={<EyeOutlined />} onClick={openModal} type='primary' />
      <BaseModal open={open} title={'Xem chi tiết khách hàng'} onClose={closeModal} childrenBody={<MemberDetail data={data} />} />
    </>
  )
}

export default DetailButton
