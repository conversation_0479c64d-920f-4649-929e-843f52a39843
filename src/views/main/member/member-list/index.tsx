import { useState, FC, useEffect } from 'react'
import { Col, Popconfirm, Row, Tag } from 'antd'
import type { ColumnsType } from 'antd/es/table'
import BaseView from '~/components/BaseView'
import BaseTable from '~/components/BaseTable'
import { IMember } from '~/dto/member.dto'
import { BaseButton } from '~/components'
import { LockOutlined, UnlockOutlined } from '@ant-design/icons'
import DetailButton from './component/DetailButton'
import { useMember } from '../Hooks/useMember'
import { enumData } from '~/common/enums/enumData'
import BaseFilter from '~/components/BaseFilter/BaseFilter'
import { useMemberFilterConfig } from '../Hooks/useOrderFilterConfig'

interface IFilterMember {
  pageIndex: number
  pageSize: number
}

type IProps = {}

const ListMemberView: FC<IProps> = () => {
  const { data, total, isLoading, useBlockMember, useUnblockMember, setFilter } = useMember(true)
  const { mutateAsync: blockMember } = useBlockMember()
  const { mutateAsync: unblockMember } = useUnblockMember()
  const { filterData, filterFields, handleFilter, handleFilterReset } = useMemberFilterConfig()
  // const { data, isLoading, total } = useListMember()
  useEffect(() => {
    setFilter(filterData)
  }, [filterData])

  const handlePageChange = (newPageIndex: number, newPageSize: number) => {
    setFilter({
      pageIndex: newPageIndex,
      pageSize: newPageSize
    })
  }

  const columns: ColumnsType<IMember> = [
    {
      title: 'STT',
      key: 'stt',
      width: 60,
      align: 'center',
      render: (_, __, index) => index + 1
    },

    {
      title: 'Họ và tên',
      dataIndex: 'fullName',
      key: 'fullName',
      width: 200,
      align: 'center'
    },
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email',
      width: 200,
      align: 'center'
    },
    {
      title: 'Trạng thái hoạt động',
      dataIndex: 'status',
      key: 'status',
      width: 150,
      align: 'center',
      render: (value) => {
        return <Tag color={enumData.MEMBER_ACTIVE_STATUS[value]?.color}>{enumData.MEMBER_ACTIVE_STATUS[value]?.value}</Tag>
      }
    },
    {
      title: 'Trạng thái xác thực',
      dataIndex: 'statusValidate',
      key: 'statusValidate',
      width: 150,
      align: 'center',
      render: (value) => {
        return <Tag color={enumData.MEMBER_VALIDATE_STATUS[value]?.color}>{enumData.MEMBER_VALIDATE_STATUS[value]?.value}</Tag>
      }
    },

    {
      title: 'Tác vụ',
      key: 'action',
      width: 150,
      fixed: 'right',
      render: (_, record, index: number) => {
        return (
          <>
            <DetailButton data={record} />
            <Popconfirm
              title={
                record.status == enumData.MEMBER_ACTIVE_STATUS.ACTIVE.key
                  ? 'Bạn có chắc chắn muốn khóa tài khoản này không?'
                  : 'Bạn có chắc chắn muốn mở khóa tài khoản này không?'
              }
              onConfirm={() => {
                if (record.status == enumData.MEMBER_ACTIVE_STATUS.ACTIVE.key) {
                  blockMember(record.id)
                } else {
                  unblockMember(record.id)
                }
              }}>
              <BaseButton
                danger={record.status == enumData.MEMBER_ACTIVE_STATUS.ACTIVE.key}
                type='default'
                shape='circle'
                icon={record.status == enumData.MEMBER_ACTIVE_STATUS.ACTIVE.key ? <LockOutlined /> : <UnlockOutlined />}
                tooltip='Delete'
                onClick={() => {}}
              />
            </Popconfirm>
          </>
        )
      }
    }
  ]

  return (
    <BaseView>
      <BaseFilter onFilter={handleFilter} onReset={handleFilterReset} isLoading={isLoading} filters={filterFields}></BaseFilter>
      <Row gutter={16}>
        <Col span={24} style={{ marginTop: 16 }}>
          <BaseTable columns={columns} data={data} total={total} isLoading={isLoading} onPageChange={handlePageChange} />
        </Col>
      </Row>
    </BaseView>
  )
}

export default ListMemberView
