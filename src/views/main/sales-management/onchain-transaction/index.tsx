import { FC, useEffect } from 'react'
import { Col, Row, Tag } from 'antd'
import type { ColumnsType } from 'antd/es/table'
import BaseView from '~/components/BaseView'
import BaseTable from '~/components/BaseTable'
import { IPaymentTransaction } from '~/dto/paymentTransaction.dto'
import BaseFilter from '~/components/BaseFilter/BaseFilter'
import { useOnChainTransaction } from './Hooks/useOnChainTransaction'
import DetailButton from './component/DetailButton'
import moment from 'moment'
import { enumData } from '~/common/enums/enumData'
import { usePaymentTransactionFilterConfig } from '../payment-transaction/Hooks/usePaymentTransactionFilterConfig'
import { IOnChainTransaction } from '~/dto/onChainTransaction.dto'
import { useOnChainTransactionFilterConfig } from './Hooks/useOnChainTransactionFilterConfig'

type IProps = {}

const OnChainTransactionView: FC<IProps> = () => {
  // const { data, total, isLoading, loadData, setFilter } = useOnChainTransaction(true)
  // const { filterData, filterFields, handleFilter, handleFilterReset, setFilterData } = useOnChainTransactionFilterConfig()
  // // const { data, isLoading, total } = useListCustomer()

  // useEffect(() => {
  //   setFilter(filterData)
  // }, [filterData])

  // const handlePageChange = (newPageIndex: number, newPageSize: number) => {
  //   setFilterData({
  //     ...filterData,
  //     pageIndex: newPageIndex,
  //     pageSize: newPageSize
  //   })
  //   loadData()
  // }
  const data: IOnChainTransaction[] = [
    {
      email: '<EMAIL>',
      ownerAddressKey: '******************************************',
      transactionFee: '0.005',
      transactionHash: '******************************************',
      configCode: 'DEPOSIT_ETH',
      status: 'success',
      dataOnChain: { amount: '1.5 ETH' },
      dataOfChain: { user_id: 'user_001' }
    },
    {
      email: '<EMAIL>',
      ownerAddressKey: '0x2bCd34E56fA7bC9d0E1F2a3b4c5d6f7A9b0Dc1',
      transactionFee: '0.001',
      transactionHash: '******************************************',
      configCode: 'WITHDRAW_USDT',
      status: 'pending',
      dataOnChain: { amount: '100 USDT' },
      dataOfChain: { user_id: 'user_002' }
    },
    {
      email: '<EMAIL>',
      ownerAddressKey: '******************************************',
      transactionFee: '0.0007',
      transactionHash: '0xghi789jk1012mn0345pq678rs901tu234vw567xy',
      configCode: 'SWAP_TOKEN',
      status: 'failed',
      dataOnChain: { from: 'USDC', to: 'DAI' },
      dataOfChain: { user_id: 'user_003' }
    },
    {
      email: '<EMAIL>',
      ownerAddressKey: '0x4De5f6A7bC9d0E1F2a3B4c5D6e7F8a9B0C1D2E3',
      transactionFee: '0.002',
      transactionHash: '******************************************',
      configCode: 'NFT_MINT',
      status: 'success',
      dataOnChain: { collection: 'CryptoPunks' },
      dataOfChain: { user_id: 'user_004' }
    },
    {
      email: '<EMAIL>',
      ownerAddressKey: '******************************************',
      transactionFee: '0.0009',
      transactionHash: '******************************************',
      configCode: 'CLAIM_REWARDS',
      status: 'success',
      dataOnChain: { reward_type: 'staking' },
      dataOfChain: { user_id: 'user_005' }
    }
  ]
  const columns: ColumnsType<IOnChainTransaction> = [
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email',
      align: 'center'
    },

    {
      title: 'Owner Address',
      dataIndex: 'ownerAddressKey',
      key: 'ownerAddressKey',
      align: 'center'
    },
    {
      title: 'Transaction Fee',
      dataIndex: 'transactionFee',
      key: 'transactionFee',
      align: 'center',
      render: (value: any) => '$' + value
    },
    {
      title: 'Transaction Hash',
      dataIndex: 'transactionHash',
      key: 'transactionHash',
      align: 'center'
    },

    {
      title: 'Config Code',
      dataIndex: 'configCode',
      key: 'configCode',
      align: 'center'
    },

    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      align: 'center'
      // render: (value: any) => <Tag color={enumData.TRANSACTION_STATUS[value].color}>{enumData.TRANSACTION_STATUS[value].value}</Tag>
    },

    {
      title: 'Data On Chain',
      dataIndex: 'dataOnChain',
      key: 'dataOnChain',
      align: 'center',
      render: (value: any) => JSON.stringify(value)
    },

    {
      title: 'Data On Chain',
      dataIndex: 'dataOfChain',
      key: 'dataOfChain',
      align: 'center',
      render: (value: any) => JSON.stringify(value)
    }
  ]

  return (
    <BaseView>
      {/* <BaseFilter onFilter={handleFilter} onReset={handleFilterReset} isLoading={isLoading} filters={filterFields}></BaseFilter> */}
      <Row gutter={16}>
        <Col span={24} style={{ marginTop: 16 }}>
          <BaseTable columns={columns} data={data} total={0} isLoading={false} scroll={{ x: 'max-content' }} />
        </Col>
      </Row>
    </BaseView>
  )
}

export default OnChainTransactionView
