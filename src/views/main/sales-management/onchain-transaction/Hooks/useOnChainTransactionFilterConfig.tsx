import { useState } from 'react'
import { enumData } from '~/common/enums/enumData'
import { IFilter } from '~/components/BaseFilter/BaseFilter'

interface IProps {}
export const useOnChainTransactionFilterConfig = (iProps?: IProps) => {
  const handleFilter = (values: any) => {
    setFilterData(values)
  }
  const handleFilterReset = () => {
    setFilterData({})
  }

  const [filterData, setFilterData] = useState({})
  const [filterFields, setFilterFields] = useState<IFilter[]>([
    {
      key: 'code',
      name: '<PERSON><PERSON> giao dịch',
      type: enumData.FILTER_TYPE.INPUT.key
    },

    //date
    {
      key: 'createdAt',
      name: 'Thời gian',
      type: enumData.FILTER_TYPE.DATE.key
    }
  ])

  return {
    filterFields,
    filterData,
    setFilterData,
    handleFilter,
    handleFilterReset
  }
}
