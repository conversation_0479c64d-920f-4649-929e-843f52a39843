import { FC, useEffect } from 'react'
import { Col, Row, Tag } from 'antd'
import type { ColumnsType } from 'antd/es/table'
import BaseView from '~/components/BaseView'
import BaseTable from '~/components/BaseTable'
import { IPaymentTransaction, IPaymentTransactionFilter } from '~/dto/paymentTransaction.dto'
import BaseFilter, { IFilter } from '~/components/BaseFilter/BaseFilter'
import { usePaymentTransaction } from './Hooks/usePaymentTransaction'
import { usePaymentTransactionFilterConfig } from './Hooks/usePaymentTransactionFilterConfig'
import DetailButton from './component/DetailButton'
import moment from 'moment'
import { enumData } from '~/common/enums/enumData'

type IProps = {}

const PaymentTransactionView: FC<IProps> = () => {
  const { data, total, isLoading, loadData, setFilter } = usePaymentTransaction(true)
  const { filterData, filterFields, handleFilter, handleFilterReset, setFilterData } = usePaymentTransactionFilterConfig()
  // const { data, isLoading, total } = useListCustomer()

  useEffect(() => {
    setFilter(filterData)
  }, [filterData])

  const handlePageChange = (newPageIndex: number, newPageSize: number) => {
    setFilterData({
      ...filterData,
      pageIndex: newPageIndex,
      pageSize: newPageSize
    })
    loadData()
  }

  const columns: ColumnsType<IPaymentTransaction> = [
   

    {
      title: 'Email',
      dataIndex: 'member',
      key: 'customerName',
      align: 'center',
      render: (value: any) => value?.email
    },

    {
      title: 'Thời gian thanh toán',
      dataIndex: 'transactionDate',
      key: 'transactionDate',
      align: 'center',
      render: (value: any) => (value ? moment(value).format('DD/MM/YYYY') : '')
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      align: 'center',
      render: (value: any) => <Tag color={enumData.TRANSACTION_STATUS[value]?.color}>{enumData.TRANSACTION_STATUS[value]?.value}</Tag>
    },

    //gói bảo mật
    {
      title: 'Giá trị giao dịch',
      dataIndex: 'amount',
      key: 'amount',
      align: 'center',
      render: (value: any) => '$' + value
    },

    {
      title: 'Tác vụ',
      key: 'action',
      render: (record) => <DetailButton data={record} />,
      align: 'center'
    }
  ]

  return (
    <BaseView>
      <BaseFilter onFilter={handleFilter} onReset={handleFilterReset} isLoading={isLoading} filters={filterFields}></BaseFilter>
      <Row gutter={16}>
        <Col span={24} style={{ marginTop: 16 }}>
          <BaseTable columns={columns} data={data} total={total} isLoading={isLoading} onPageChange={handlePageChange} />
        </Col>
      </Row>
    </BaseView>
  )
}

export default PaymentTransactionView
