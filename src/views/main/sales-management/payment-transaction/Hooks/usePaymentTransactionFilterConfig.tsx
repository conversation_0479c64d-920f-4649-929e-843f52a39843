import { useState } from 'react'
import { enumData } from '~/common/enums/enumData'
import { IFilter } from '~/components/BaseFilter/BaseFilter'
import { getDateRangeClosed } from '~/common/helper'
import moment from 'moment'

interface IProps {}
export const usePaymentTransactionFilterConfig = (iProps?: IProps) => {
  const handleFilter = (values: any) => {
    if (values.transactionDate) {
      values.transactionDateFrom = moment(new Date(values.transactionDate[0]).setHours(0, 0, 0)).format('YYYY-MM-DD HH:mm:ss')
      values.transactionDateTo = moment(new Date(values.transactionDate[1]).setHours(23, 59, 59)).format('YYYY-MM-DD HH:mm:ss')
      delete values.transactionDate
    }
    setFilterData(values)
  }
  const handleFilterReset = () => {
    setFilterData({})
  }

  const [filterData, setFilterData] = useState({})
  const [filterFields, setFilterFields] = useState<IFilter[]>([
    {
      key: 'code',
      name: 'Mã giao dịch',
      type: enumData.FILTER_TYPE.INPUT.key
    },
    //trạng thái
    {
      key: 'status',
      name: 'Trạng thái',
      type: enumData.FILTER_TYPE.SELECT.key,
      selectOptions: Object.values(enumData.TRANSACTION_STATUS).map((status: any) => {
        return {
          key: status.key,
          value: status.value
        }
      })
    },
    //date
    {
      key: 'transactionDate',
      name: 'Thời gian thanh toán',
      type: enumData.FILTER_TYPE.DATE_RANGE.key
    }
  ])

  return {
    filterFields,
    filterData,
    setFilterData,
    handleFilter,
    handleFilterReset
  }
}
