import { useInfiniteQuery } from '@tanstack/react-query'
import { useState } from 'react'
import { IPaymentTransaction, IPaymentTransactionFilter } from '~/dto/paymentTransaction.dto'
import { paymentTransactionService } from '~/services/PaymentTransactionService/paymentTransactionService'

export const usePaymentTransaction = (enabled: boolean = false) => {
  const [filter, setFilter] = useState<IPaymentTransactionFilter>({})
  const { data, isFetching, refetch } = useInfiniteQuery({
    queryKey: [paymentTransactionService.APIs.LIST, filter],
    queryFn: async () => {
      return await paymentTransactionService.getPagination(filter)
    },
    getNextPageParam: (lastPage, allPages) => {
      if (lastPage.data.length === 0) return undefined
      return allPages.length + 1
    },
    initialPageParam: 1,
    enabled: enabled
  })

  const getDetail = async (id: string): Promise<IPaymentTransaction> => {
    return await paymentTransactionService.getDetail(id)
  }

  const formattedData = data?.pages.flatMap((page) => page.data)
  const total = data?.pages[0].total

  return {
    data: formattedData,
    total: total,
    isLoading: isFetching,
    loadData: refetch,
    setFilter,
    getDetail
  }
}
