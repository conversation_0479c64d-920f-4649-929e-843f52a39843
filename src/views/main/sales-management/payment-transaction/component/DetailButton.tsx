import { EyeOutlined } from '@ant-design/icons'
import { Card, Row, Col, Typography, Modal, Button, Input, Tag } from 'antd'
import moment from 'moment'
import { enumData } from '~/common/enums/enumData'
import BaseButton from '~/components/BaseButton'
import { IPaymentTransaction } from '~/dto/paymentTransaction.dto'
import { useModal } from '~/views/global-hooks/useModal'
import { usePaymentTransaction } from '../Hooks/usePaymentTransaction'
import { useEffect, useState } from 'react'

const { Text } = Typography

interface DetailButtonProps {
  data: IPaymentTransaction
}

const DetailButton = ({ data }: DetailButtonProps) => {
  const { open, openModal, closeModal } = useModal()
  const { getDetail } = usePaymentTransaction()
  const [detail, setDetail] = useState<IPaymentTransaction>()
  useEffect(() => {
    if (!open) return
    getDetail(data.id).then((res) => {
      setDetail(res)
    })
  }, [open])

  const modalContent = detail && (
    <Card>
      <Row gutter={16}>
        <Col span={8} style={{ marginBottom: 15 }}>
          <Text strong>Email:</Text>
          <p>{data.member.email}</p>
        </Col>

        <Col span={8} style={{ marginBottom: 15 }}>
          <Text strong>Ngày tạo giao dịch:</Text>
          <p>{moment(data.createdDate).format('DD/MM/YYYY')}</p>
        </Col>

        <Col span={8} style={{ marginBottom: 15 }}>
          <Text strong>Trạng thái:</Text>
          <p>
            <Tag color={enumData.TRANSACTION_STATUS[data.status]?.color}>{enumData.TRANSACTION_STATUS[data.status]?.value}</Tag>
          </p>
        </Col>

        <Col span={8} style={{ marginBottom: 15 }}>
          <Text strong>Giá trị giao dịch:</Text>
          <p>${data.amount}</p>
        </Col>

        <Col span={8} style={{ marginBottom: 15 }}>
          <Text strong>Phương thức thanh toán:</Text>
          <p>{data.paymentMethod}</p>
        </Col>

        <Col span={8} style={{ marginBottom: 15 }}>
          <Text strong>Cổng thanh toán:</Text>
          <p>{data.paymentProvider}</p>
        </Col>

        <Col span={8} style={{ marginBottom: 15 }}>
          <Text strong>Mã tham chiếu:</Text>
          <div style={{ marginTop: 10 }}>
            <Input.Password visibilityToggle={true} type='password' value={data.sessionRefId} style={{ width: '70%' }} />
          </div>
        </Col>

        {/* <Col span={8} style={{ marginBottom: 15 }}>
          <Text strong>Khóa giao dịch:</Text>
          <div style={{ marginTop: 10 }}>
            <Input.Password visibilityToggle={true} type='password' value={data.clientSecret} style={{ width: '70%' }} />
          </div>
        </Col> */}
      </Row>
    </Card>
  )

  return (
    <>
      <BaseButton icon={<EyeOutlined />} onClick={openModal} type='primary' />

      <Modal title='Chi tiết giao dịch (Payment)' open={open} onCancel={closeModal} footer={null} width={1000}>
        {modalContent}
      </Modal>
    </>
  )
}

export default DetailButton
