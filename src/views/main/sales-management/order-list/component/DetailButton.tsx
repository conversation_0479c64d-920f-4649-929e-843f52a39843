import { EyeOutlined } from '@ant-design/icons'
import BaseButton from '~/components/BaseButton'
import { IPaymentTransaction } from '~/dto/paymentTransaction.dto'
import { IOrder } from '~/dto/order.dto'
import { useModal } from '~/views/global-hooks/useModal'
import { Card, Col, Input, Modal, Row, Tag, Typography } from 'antd'
import moment from 'moment'
import { enumData } from '~/common/enums/enumData'
import { useOrder } from '../Hooks/useOrder'
import { useState, useEffect } from 'react'

interface DetailButtonProps {
  data: IOrder
}
const { Text } = Typography
const DetailButton = ({ data }: DetailButtonProps) => {
  const { getDetail } = useOrder()
  const { open, openModal, closeModal } = useModal()
  const [detail, setDetail] = useState<IOrder>()
  const [loaded, setLoaded] = useState(false)
  useEffect(() => {
    if (open === false) return
    getDetail(data.id).then((res) => {
      setDetail(res)
      setLoaded(true)
    })
  }, [open])

  const modalContent = loaded && (
    <Card>
      <Row gutter={16}>
        <Col span={8} style={{ marginBottom: 15 }}>
          <Text strong>Email:</Text>
          <p>{data.member?.email}</p>
        </Col>
        <Col span={8} style={{ marginBottom: 15 }}>
          <Text strong>Tổng tiền chưa VAT:</Text>
          <p>${detail.totalPrice}</p>
        </Col>

        <Col span={8} style={{ marginBottom: 15 }}>
          <Text strong>VAT:</Text>
          <p>${detail.vat}</p>
        </Col>

        <Col span={8} style={{ marginBottom: 15 }}>
          <Text strong>Tổng tiền sau VAT:</Text>
          <p>${detail.totalPriceVat}</p>
        </Col>
        <Col span={8} style={{ marginBottom: 15 }}>
          <Text strong>Ngày đặt hàng:</Text>
          <p>{moment(detail.createdAt).format('DD/MM/YYYY')}</p>
        </Col>
        {detail?.paymentDate && (
          <Col span={8} style={{ marginBottom: 15 }}>
            <Text strong>Ngày thanh toán:</Text>
            <p>{(detail?.paymentDate && moment(detail?.paymentDate).format('DD/MM/YYYY')) || ''}</p>
          </Col>
        )}

        <Col span={8} style={{ marginBottom: 15 }}>
          <Text strong>Phương thức thanh toán:</Text>
          <p>{detail.paymentMethod}</p>
        </Col>

        <Col span={8} style={{ marginBottom: 15 }}>
          <Text strong>Trạng thái thanh toán:</Text>
          <p>
            <Tag color={enumData.ORDER_PAYMENT_STATUS[detail.paymentStatus]?.color} style={{ fontSize: 14, padding: 5 }}>
              {enumData.ORDER_PAYMENT_STATUS[detail.paymentStatus]?.value}
            </Tag>
          </p>
        </Col>

        {/* <Col span={8} style={{ marginBottom: 15 }}>
          <Text strong>Trạng thái:</Text>
          <p>
            <Tag color={enumData.TRANSACTION_STATUS[detail.status].color} style={{ fontSize: 14, padding: 5 }}>
              {enumData.TRANSACTION_STATUS[detail.status].value}
            </Tag>
          </p>
        </Col> */}
      </Row>
    </Card>
  )

  return (
    <>
      <BaseButton icon={<EyeOutlined />} onClick={openModal} type='primary' />
      <Modal title='Chi tiết đơn hàng' open={open} onCancel={closeModal} footer={null} width={1000}>
        {modalContent}
      </Modal>
    </>
  )
}

export default DetailButton
