import { useState, FC, useEffect } from 'react'
import { Button, Col, Row, Tag } from 'antd'
import type { ColumnsType } from 'antd/es/table'
import BaseView from '~/components/BaseView'
import BaseTable from '~/components/BaseTable'
import BaseFilter, { IFilter } from '~/components/BaseFilter/BaseFilter'
import { IOrder, IOrderFilter } from '~/dto/order.dto'
import { useOrder } from './Hooks/useOrder'
import { useOrderFilterConfig } from './Hooks/useOrderFilterConfig'
import moment from 'moment'
import { enumData } from '~/common/enums/enumData'
import DetailButton from './component/DetailButton'

type IProps = {}

const OrderListView: FC<IProps> = () => {
  const { data, isFetching, refetch, setFilter, total } = useOrder(true)
  // const { data, isLoading, total } = useListCustomer()
  const { filterData, filterFields, handleFilter, handleFilterReset, setFilterData } = useOrderFilterConfig()

  useEffect(() => {
    setFilter(filterData)
  }, [filterData])

  const handlePageChange = (newPageIndex: number, newPageSize: number) => {
    setFilterData({
      ...filterData,
      pageIndex: newPageIndex,
      pageSize: newPageSize
    })
    refetch()
  }

  

  const columns: ColumnsType<IOrder> = [
    {
      title: 'Email',
      dataIndex: 'member',
      align: 'center',
      key: 'member',
      render: (value: any) => {
        return value?.email || ''
      }
    },
    {
      title: 'Số tiền',
      dataIndex: 'totalPrice',
      align: 'center',
      key: 'totalPrice',
      render: (text) => {
        return `$${text}`
      }
    },

    {
      title: 'Ngày đặt hàng',
      dataIndex: 'createdDate',
      align: 'center',
      key: 'createdDate',
      render: (text) => {
        return text ? moment(text).format('DD/MM/YYYY') : null
      }
    },
    {
      title: 'Ngày thanh toán',
      dataIndex: 'paymentDate',
      align: 'center',
      key: 'paymentDate',
      render: (text) => {
        return text ? moment(text).format('DD/MM/YYYY') : null
      }
    },
    {
      title: 'Trạng thái',
      dataIndex: 'paymentStatus',
      align: 'center',
      key: 'paymentStatus',
      render: (text) => {
        return <Tag color={enumData.ORDER_PAYMENT_STATUS[text]?.color}>{enumData.ORDER_PAYMENT_STATUS[text]?.value}</Tag>
      }
    },
    //tác vụ
    {
      title: 'Tác vụ',
      align: 'center',
      render: (text, record) => {
        return (
          <>
            <DetailButton data={record} />
            <Button type='primary'>Lấy hóa đơn</Button>
          </>
        )
      }
    }
  ]

  return (
    <BaseView>
      <BaseFilter onFilter={handleFilter} onReset={handleFilterReset} isLoading={isFetching} filters={filterFields}></BaseFilter>
      <Row gutter={16}>
        <Col span={24} style={{ marginTop: 16 }}>
          <BaseTable columns={columns} data={data} total={total} isLoading={isFetching} onPageChange={handlePageChange} />
        </Col>
      </Row>
    </BaseView>
  )
}

export default OrderListView
