import moment from 'moment'
import { useState } from 'react'
import { enumData } from '~/common/enums/enumData'
import { getDateRangeClosed, normalizeEndDay, normalizeStartDay } from '~/common/helper'
import { IFilter } from '~/components/BaseFilter/BaseFilter'

interface IProps {}
export const useOrderFilterConfig = (iProps?: IProps) => {
  const handleFilter = (values: any) => {
    if (values.paymentDate) {
      values.paymentDateFrom = moment(new Date(values.paymentDate[0]).setHours(0, 0, 0)).format('YYYY-MM-DD HH:mm:ss')
      values.paymentDateTo = moment(new Date(values.paymentDate[1]).setHours(23, 59, 59)).format('YYYY-MM-DD HH:mm:ss')
      delete values.paymentDate
    }

    if (values.createdDate) {
      values.createdDateFrom = moment(new Date(values.createdDate[0]).setHours(0, 0, 0)).format('YYYY-MM-DD HH:mm:ss')
      values.createdDateTo = moment(new Date(values.createdDate[1]).setHours(23, 59, 59)).format('YYYY-MM-DD HH:mm:ss')
      delete values.createdDate
    }

    //filter null
    values = Object.fromEntries(Object.entries(values).filter(([_, v]) => v != null))
    setFilterData(values)
  }

  const handleFilterReset = () => {
    setFilterData({})
  }

  const [filterData, setFilterData] = useState({})
  const [filterFields, setFilterFields] = useState<IFilter[]>([
    {
      key: 'code',
      name: 'Mã đơn hàng',
      type: enumData.FILTER_TYPE.INPUT.key
    },

    //Ngày đặt hàng
    {
      key: 'createdDate',
      name: 'Ngày đặt hàng',
      type: enumData.FILTER_TYPE.DATE_RANGE.key
    },
    //ngày thanh toán
    {
      key: 'paymentDate',
      name: 'Ngày thanh toán',
      type: enumData.FILTER_TYPE.DATE_RANGE.key
    },
    //trang thai
    {
      key: 'paymentStatus',
      name: 'Trạng thái',
      type: enumData.FILTER_TYPE.SELECT.key,
      selectOptions: Object.values(enumData.ORDER_PAYMENT_STATUS).map((status: any) => {
        return {
          key: status.key,
          value: status.value
        }
      })
    }
  ])

  return {
    filterFields,
    filterData,
    setFilterData,
    handleFilter,
    handleFilterReset
  }
}
