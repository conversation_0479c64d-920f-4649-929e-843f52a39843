import { useInfiniteQuery } from '@tanstack/react-query'
import { useState } from 'react'
import { IOrderFilter } from '~/dto/order.dto'
import { orderService } from '~/services/OrderService/orderService'

export const useOrder = (enableUpdate: boolean = false) => {
  const [filter, setFilter] = useState<IOrderFilter>({})
  const { data, isFetching, refetch } = useInfiniteQuery({
    queryKey: [orderService.APIs.LIST, filter],
    queryFn: async () => {
      return await orderService.getPagination(filter)
    },
    getNextPageParam: (lastPage, allPages) => {
      if (lastPage.data.length === 0) return undefined
      return allPages.length + 1
    },
    initialPageParam: 1,
    enabled: enableUpdate
  })

  const getDetail = async (id: string) => {
    return await orderService.getDetail(id)
  }

  const formattedData = data?.pages.flatMap((page) => page.data)
  const total = data?.pages[0].total

  return {
    data: formattedData,
    total: total,
    isFetching,
    refetch,
    setFilter,
    getDetail
  }
}
