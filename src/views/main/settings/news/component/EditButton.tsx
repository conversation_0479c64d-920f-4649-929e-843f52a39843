import { EditOutlined } from '@ant-design/icons'
import { useState } from 'react'
import { BaseButton } from '~/components'
import { INews } from '~/dto/news.dto'
import { CreateOrEditNewsView } from './CreateOrEditNewsView'
import { useNews } from '../Hooks/useNews'
import { useQueryClient } from '@tanstack/react-query'
import { newsService } from '~/services/NewsService/newsService'

interface EditButtonProps {
  data: INews
  isLoading?: boolean
}

const EditButton = ({ data, isLoading = false }: EditButtonProps) => {
  const [open, setOpen] = useState(false)
  const clickOpen = () => {
    setOpen(true)
  }

  const clickClose = async () => {
    setOpen(false)
  }

  return (
    <>
      <BaseButton icon={<EditOutlined />} onClick={clickOpen} type='primary' tooltip={''} />
      <CreateOrEditNewsView data={data} open={open} onClose={clickClose} />
    </>
  )
}

export default EditButton
