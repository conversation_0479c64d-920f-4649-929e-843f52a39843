import { SaveOutlined } from '@ant-design/icons'
import { Button, Col, Form, Input, Row, Spin, Upload } from 'antd'
import { useForm } from 'antd/es/form/Form'
import TextArea from 'antd/es/input/TextArea'
import { FC, useEffect } from 'react'
import BaseModal from '~/components/BaseModal'
import { INews } from '~/dto/news.dto'
import { useNews } from '../Hooks/useNews'
import ReactQuill from 'react-quill'
interface IProps {
  data?: INews
  open: boolean
  onClose: () => void
}
export const CreateOrEditNewsView: FC<IProps> = ({ data, open, onClose }: IProps) => {
  const { loadData, useUpdateData, useCreateData } = useNews()
  const { mutateAsync: updateData, isPending: isUpdating } = useUpdateData()
  const { mutateAsync: createData, isPending: isCreating } = useCreateData()
  const [form] = useForm<INews>()

  useEffect(() => {
    if (open && data) {
      form.setFieldsValue({
        ...data
      })
    } else {
      form.resetFields()
    }
  }, [open, data, form])

  const handleSave = async (values: INews) => {
    if (data && data != null) {
      const modifiedValues = {
        ...data,
        ...values
      }
      updateData(modifiedValues)
    } else {
      await createData(values)
      handleCancel()
    }
  }

  const handleCancel = async () => {
    form.resetFields()
    loadData()
    onClose()
  }

  const modalContent = (
    <Spin spinning={data != null ? isUpdating : isCreating}>
      <Form form={form} layout='vertical' onFinish={handleSave}>
        <Row gutter={24}>
          <Col span={12}>
            <Form.Item
              label='Tiêu đề'
              name='title'
              rules={[{ required: true, message: 'Vui lòng nhập tiêu đề!' }]}>
              <Input placeholder='Nhập tiêu đề' />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label='Hinh ảnh' name='thumbnailUrl'>
              {/* Nhập file hình ảnh */}
              <Upload>
                <Button>Chọn hình ảnh</Button>
              </Upload>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label='Mô tả ngắn' name='shortDescription'>
              <TextArea rows={4} placeholder='Nhập mô tả' />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item
              label='Nội dung tin tức'
              name='contentHtml'
              rules={[{ required: true, message: 'Vui lòng nhập nội dung tin tức!' }]}>
              {/* <TextArea rows={10} placeholder='Nhập nội dung tin tức' /> */}
              <ReactQuill theme='snow' style={{ height: '300px' }} />
            </Form.Item>
          </Col>
        </Row>
        <Row
          style={{
            marginTop: 24,
            borderTop: '1px solid #f0f0f0',
            paddingTop: 16
          }}
          justify={'center'}>
          <Button onClick={handleCancel} style={{ marginRight: 8 }}>
            Hủy
          </Button>
          <Button type='primary' htmlType='submit' icon={<SaveOutlined />}>
            {data ? 'Cập nhật' : 'Tạo mới'}
          </Button>
        </Row>
      </Form>
    </Spin>
  )
  return (
    <BaseModal
      open={open}
      onClose={handleCancel}
      title={data ? 'Chỉnh sửa tin tức' : 'Tạo mới tin tức'}
      description={data ? 'Chỉnh sửa tin tức' : 'Tạo mới tin tức'}
      childrenBody={modalContent}
    />
  )
}
