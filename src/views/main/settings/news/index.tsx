import { ColumnsType } from 'antd/es/table'
import { FC, useEffect } from 'react'
import BaseTable from '~/components/BaseTable'
import BaseView from '~/components/BaseView'
import { INews } from '~/dto/news.dto'
import EditButton from './component/EditButton'
import { Button, Popconfirm, Tag, Tooltip } from 'antd'
import { PlayCircleOutlined, StopOutlined } from '@ant-design/icons'
import BaseFilter from '~/components/BaseFilter/BaseFilter'
import { useNews } from './Hooks/useNews'
import { useNewsFilterConfig } from './Hooks/useNewsFilterConfig'
import { enumData } from '~/common/enums/enumData'

interface IProps {}
const NewsView: FC<IProps> = function () {
  const { data, total, isLoading, loadData, setFilterData } = useNews(true)
  const { mutateAsync: changeStatus, isPending } = useNews().useChangeStatus()
  const { filterFields, filterData, handlePageChange, handleFilter, handleFilterReset } = useNewsFilterConfig()

  useEffect(() => {
    loadData()
  }, [])

  useEffect(() => {
    setFilterData(filterData)
  }, [filterData])

  const handleChangeStatus = (record: INews) => {
    changeStatus(record).then(() => loadData())
  }
  const columns: ColumnsType<INews> = [
    //tiêu đề
    {
      title: 'Tiêu đề',
      dataIndex: 'title',
      key: 'title',
      align: 'center'
    },

    //trạng thái
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      align: 'center',
      render: (value: string) => {
        return (
          <Tag style={{ fontSize: 14, padding: 5 }} color={enumData.NEWS_STATUS[value]?.color}>
            {enumData.NEWS_STATUS[value]?.value}
          </Tag>
        )
      }
    },
    //Mô tả ngắn
    {
      title: 'Mô tả ngắn',
      dataIndex: 'shortDescription',
      key: 'shortDescription',
      align: 'center'
    },
    //tác vụ
    {
      title: 'Tác vụ',
      align: 'center',
      render: (_, record) => {
        return (
          <>
            <EditButton data={record} isLoading={isLoading} />
            {record.status === enumData.NEWS_STATUS.INACTIVE.key || record.status === enumData.NEWS_STATUS.DRAFT.key ? (
              <Popconfirm title='Bạn có chắc chắn muốn đăng tin tức này không?' onConfirm={() => handleChangeStatus(record)}>
                <Tooltip title='Hoạt động'>
                  <Button icon={<PlayCircleOutlined />} type='default'></Button>
                </Tooltip>
              </Popconfirm>
            ) : (
              <Popconfirm title='Bạn có chắc chắn muốn ngưng hoạt động tin tức này không?' onConfirm={() => handleChangeStatus(record)}>
                <Tooltip title='Ngưng hoạt động'>
                  <Button icon={<StopOutlined />} type='default' danger></Button>
                </Tooltip>
              </Popconfirm>
            )}
          </>
        )
      }
    }
  ]

  return (
    <BaseView>
      <BaseFilter onFilter={handleFilter} onReset={handleFilterReset} isLoading={isLoading} filters={filterFields}></BaseFilter>
      <BaseTable columns={columns} data={data} total={total} isLoading={isLoading} onPageChange={handlePageChange} />
    </BaseView>
  )
}

export default NewsView
