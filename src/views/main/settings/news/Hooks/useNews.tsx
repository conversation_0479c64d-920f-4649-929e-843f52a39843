import { useEffect, useMemo, useState } from 'react'
import { INews, INewsFilter, INewsList } from '~/dto/news.dto'
import { newsService } from '~/services/NewsService/newsService'
import { enumData } from '~/common/enums/enumData'
import { toastService } from '~/services'
import { useInfiniteQuery, useMutation, useQuery, useQueryClient } from '@tanstack/react-query'

export const useNews = (enabled: boolean = false) => {
  // const [data, setData] = useState<INewsList>()
  const [filterData, setFilterData] = useState<INewsFilter>({})
  const { data, isFetching, refetch } = useInfiniteQuery<INewsList, Error>({
    queryKey: [newsService.APIs.LIST, JSON.stringify(filterData)],
    queryFn: async () => {
      return await newsService.getPaginationNews(filterData)
    },
    getNextPageParam: (lastPage, allPages) => {
      if (lastPage.data.length === 0) return undefined
      return allPages.length + 1
    },
    initialPageParam: 1,
    enabled: enabled
  })

  //change status
  const useChangeStatus = () => {
    {
      return useMutation({
        mutationFn: async (record: INews) => {
          if (
            record.status === enumData.NEWS_STATUS.INACTIVE.key ||
            record.status === enumData.NEWS_STATUS.DRAFT.key
          ) {
            await newsService.setActiveNews(record.id)
          } else {
            await newsService.setInactiveNews(record.id)
          }
        },
        onSuccess: () => {
          toastService.success('Cập nhật trạng thái tin tức thành công')
        },
        onError: () => {
          toastService.error('Cập nhật trạng thái tin tức thất bại')
        }
      })
    }
  }

  //create news
  const useCreateData = () => {
    return useMutation({
      mutationFn: async (record: INews) => {
        await newsService.createNews(record)
      },
      onSuccess: () => {
        toastService.success('Tạo mới tin tức thành công')
      },
      onError: () => {
        toastService.error('Tạo mới tin tức thất bại')
      }
    })
  }

  //update news
  const useUpdateData = () => {
    return useMutation({
      mutationFn: async (record: INews) => {
        await newsService.updateNews(record)
      },
      onSuccess: () => {
        toastService.success('Cập nhật tin tức thành công')
      },
      onError: () => {
        toastService.error('Cập nhật tin tức thất bại')
      }
    })
  }

  //load pagination khi filterData thay đổi

  return {
    data: data?.pages.flatMap((page) => page.data),
    total: data?.pages[0]?.total ?? 0,
    isLoading: isFetching,
    loadData: refetch,
    useChangeStatus,
    useUpdateData,
    useCreateData,
    setFilterData
  }
}
