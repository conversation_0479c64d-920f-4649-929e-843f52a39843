import { useEffect, useState } from 'react'
import { IFilter } from '~/components/BaseFilter/BaseFilter'
import { useNews } from './useNews'
import { enumData } from '~/common/enums/enumData'

interface IProps {}

export const useNewsFilterConfig = (iProps?: IProps) => {
  const [filterData, setFilterData] = useState({})

  const handleFilter = (values: any) => {
    setFilterData({
      ...(values.title && { title: values.title }),
      ...(values.status && { status: values.status })
    })
  }

  const handleFilterReset = () => {
    setFilterData({})
  }

  const handlePageChange = (pageIndex: number, pageSize: number) => {
    setFilterData({ ...filterData, pageIndex: pageIndex, pageSize: pageSize })
  }

  const [filterFields, setfilterFields] = useState<IFilter[]>([
    {
      name: 'Tiêu đề',
      type: 'input',
      key: 'title'
    },

    {
      name: 'Trạng thái',
      type: 'select',
      key: 'status',
      selectOptions: Object.values(enumData.NEWS_STATUS).map((status: any) => {
        return {
          key: status.key,
          value: status.value
        }
      })
    }
  ])

  return {
    filterFields,
    filterData,
    setFilterData,
    handleFilter,
    handleFilterReset,
    handlePageChange
  }
}
