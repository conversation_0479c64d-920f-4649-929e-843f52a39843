import { useInfiniteQuery, useMutation } from '@tanstack/react-query'
import { useEffect, useState } from 'react'
import { ISubscriptionPlan, ISubscriptionPlanFilter, ISubscriptionPlanResponse } from '~/dto/subscriptionPlan.dto'
import { toastService } from '~/services'
import { subscriptionPlanService } from '~/services/SubsciptionPlanService/subscriptionPlanService'

export const useSubscriptionPlan = ({ enabled = false, id = '' }) => {
  const [filter, setFilter] = useState<ISubscriptionPlanFilter>({})
  const { data, isFetching, refetch } = useInfiniteQuery<ISubscriptionPlanResponse>({
    queryKey: [subscriptionPlanService.APIs.LIST, filter],
    queryFn: async () => {
      return await subscriptionPlanService.pagination(filter)
    },
    getNextPageParam: (lastPage, allPages) => {
      if (lastPage.data.length === 0) return undefined
      return allPages.length + 1
    },
    initialPageParam: 1,
    enabled: enabled
  })
  const [details, setDetails] = useState([])
  const [loadingDetail, setLoadingDetail] = useState(false)

  // get detail
  const getDetail = async (id: string) => {
    setLoadingDetail(true)
    let data = await subscriptionPlanService.getDetail(id);
    setDetails(data.details)
    setLoadingDetail(false)
  }

  const setActive = async (id: string) => {
    return await subscriptionPlanService.setActive(id).then(() => {
      refetch()
    })
  }

  const setInactive = async (id: string) => {
    return await subscriptionPlanService.setInactive(id).then(() => {
      refetch()
    })
  }

  const useUpdate = () => {
    return useMutation({
      mutationFn: async (data: ISubscriptionPlan) => {
        console.log(data)
        return await subscriptionPlanService.update(data)
      },
      onSuccess: () => {
        toastService.success('Cập nhật thông tin gói dịch vụ thành công')
      },
      onError: (error) => {
        toastService.error(error.message)
      }
    })
  }

  const useCreate = () => {
    return useMutation({
      mutationFn: async (data: ISubscriptionPlan) => {
        return await subscriptionPlanService.create(data)
      },
      onSuccess: () => {
        refetch()
        toastService.success('Tạo gói dịch vụ thành công')
      },
      onError: (error) => {
        toastService.error(error.message)
      }
    })
  }
  const formattedData = data?.pages.flatMap((page) => page.data)
  const total = data?.pages[0].total

  useEffect(() => {
    if (id) {
      getDetail(id)
    }
  }, [id])

  return {
    data: formattedData,
    total,
    isFetching,
    loadData: refetch,
    setFilter,
    useUpdate,
    useCreate,
    setActive,
    setInactive,
    getDetail,
    details,
    loadingDetail
  }
}
