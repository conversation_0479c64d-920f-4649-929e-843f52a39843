import { useState } from 'react'
import { IFilter } from '~/components/BaseFilter/BaseFilter'
import { enumData } from '~/common/enums/enumData'
import { ISubscriptionPlan } from '~/dto/subscriptionPlan.dto'

interface IProps {}
export const useSubcriptionPlanFilterConfig = (iProps?: IProps) => {
  const handleFilter = (values: ISubscriptionPlan) => {
    setFilterData(values)
  }
  const handleFilterReset = () => {
    setFilterData({})
  }

  const [filterData, setFilterData] = useState({})

  const [filterFields, setFilterFields] = useState<IFilter[]>([
    {
      key: 'code',
      name: '<PERSON>ã gói',
      type: enumData.FILTER_TYPE.INPUT.key
    },
    {
      key: 'name',
      name: 'Tên gói',
      type: enumData.FILTER_TYPE.INPUT.key
    },

    //trang thai
    {
      key: 'status',
      name: 'Trạng thái',
      type: enumData.FILTER_TYPE.SELECT.key,
      selectOptions: Object.values(enumData.PACKAGE_STATUS).map((status: any) => {
        return {
          key: status.key,
          value: status.value
        }
      })
    }
  ])

  return {
    filterFields,
    filterData,
    setFilterData,
    handleFilter,
    handleFilterReset
  }
}
