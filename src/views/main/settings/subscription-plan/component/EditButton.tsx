import { EditOutlined } from '@ant-design/icons'
import { useState } from 'react'
import { BaseButton } from '~/components'
import { ISubscriptionPlan } from '~/dto/subscriptionPlan.dto'
import { CreateOrEditSubscriptionPlanView } from './CreateOrEditSubscriptionPlanView'
import { subscriptionPlanService } from '~/services/SubsciptionPlanService/subscriptionPlanService'

interface EditButtonProps {
  data: ISubscriptionPlan
  onSuccess?: () => void
  onClose?: () => void
}

const EditButton = ({ data, onClose }: EditButtonProps) => {
  const [open, setOpen] = useState(false)
  const [detailedData, setDetailedData] = useState<ISubscriptionPlan | null>(null)
  const [loading, setLoading] = useState(false)

  const openModal = async () => {
    setOpen(true)
    setLoading(true)
    // Fetch detailed data including details array
    try {
      const fullData = await subscriptionPlanService.getDetail(data.id)
      setDetailedData({
        ...data,
        details: fullData.details || []
      })
    } catch (error) {
      console.error('Error fetching subscription plan details:', error)
      // Fallback to basic data if API call fails
      setDetailedData({
        ...data,
        details: data.details || []
      })
    } finally {
      setLoading(false)
    }
  }

  const closeModal = () => {
    setOpen(false)
    setDetailedData(null)
    onClose && onClose()
  }

  return (
    <>
      <BaseButton
        icon={<EditOutlined />}
        onClick={openModal}
        type='primary'
        loading={loading}
        tooltip={
          'Nếu là gói dùng thử thì tick chọn checkbox "Dùng thử"  không cần nhập giá (hệ thống tự điền = 0) Nếu không phải là gói dùng thử phải nhập 1 trong 2 giá bán (tháng/năm)'
        }
      />
      <CreateOrEditSubscriptionPlanView data={detailedData || data} open={open} onClose={closeModal} />
    </>
  )
}

export default EditButton
