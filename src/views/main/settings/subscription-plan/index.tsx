import { FC, useEffect } from 'react'
import { Col, Popconfirm, Row, Tag } from 'antd'
import type { ColumnsType } from 'antd/es/table'
import BaseView from '~/components/BaseView'
import BaseTable from '~/components/BaseTable'
import BaseFilter from '~/components/BaseFilter/BaseFilter'
import { enumData } from '~/common/enums/enumData'
import DetailButton from './component/DetailButton'
import { ISubscriptionPlan } from '~/dto/subscriptionPlan.dto'
import EditButton from './component/EditButton'
import { useSubscriptionPlan } from './Hooks/useSubscriptionPlan'
import { useSubcriptionPlanFilterConfig } from './Hooks/useSubcriptionPlanFilterConfig'
import { BaseButton } from '~/components'
import { PlayCircleOutlined, StopOutlined } from '@ant-design/icons'
import moment from 'moment'

type IProps = {}

const SubscriptionPlanView: FC<IProps> = () => {
  const { data, isFetching, loadData, setFilter, setActive, setInactive } = useSubscriptionPlan({ enabled: true })
  const { filterFields, filterData, handleFilter, handleFilterReset } = useSubcriptionPlanFilterConfig()

  const handlePageChange = (newPageIndex: number, newPageSize: number) => {
    setFilter({
      ...filterData,
      pageIndex: newPageIndex,
      pageSize: newPageSize
    })
  }

  useEffect(() => {
    setFilter(filterData)
    loadData()
  }, [filterData])

  const columns: ColumnsType<ISubscriptionPlan> = [
    {
      title: 'Mã gói',
      dataIndex: 'code',
      align: 'center',
      key: 'code',
      width: '10%'
    },
    {
      title: 'Tên gói',
      dataIndex: 'name',
      align: 'center',
      key: 'name'
    },
    {
      title: 'Miêu tả',
      dataIndex: 'note',
      align: 'center',
      key: 'note'
    },
    {
      title: 'Giá gốc',
      align: 'center',
      render: (record) => <div>${record.originalPrice}</div>
    },
    //trạng thái
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      align: 'center',
      key: 'status',
      render: (status) => (
        <Tag style={{ fontSize: 14, padding: 5 }} color={enumData.PACKAGE_STATUS[status]?.color}>
          {enumData.PACKAGE_STATUS[status]?.value}
        </Tag>
      )
    },
    {
      title: 'Ngày tạo',
      dataIndex: 'createdDate',
      align: 'center',
      key: 'createdDate',
      render: (value) => (value ? moment(value).format('DD/MM/YYYY') : '')
    },
    {
      title: 'Cập nhật lần cuối',
      dataIndex: 'updatedDate',
      align: 'center',
      key: 'updatedDate',
      render: (value) => (value ? moment(value).format('DD/MM/YYYY HH:mm:ss') : '')
    },
    {
      title: 'Tác vụ',
      key: 'action',
      align: 'center',
      render: (record) => {
        return (
          <>
            <EditButton data={record} onClose={loadData} />
            <DetailButton data={record} />
            <Popconfirm
              title={
                record.status === enumData.PACKAGE_STATUS.ACTIVE.key
                  ? 'Bạn có chắc chắn muốn ngưng hoạt động gói này?'
                  : 'Bạn có chắc chắn muốn cho hoạt động lại gói này?'
              }
              onConfirm={() => {
                if (record.status === enumData.PACKAGE_STATUS.ACTIVE.key) {
                  setInactive(record.id)
                } else {
                  setActive(record.id)
                }
              }}>
              <BaseButton
                onClick={() => {}}
                type='default'
                danger={record.status === enumData.PACKAGE_STATUS.ACTIVE.key}
                icon={record.status === enumData.PACKAGE_STATUS.ACTIVE.key ? <StopOutlined /> : <PlayCircleOutlined />}
                tooltip={record.status === enumData.PACKAGE_STATUS.ACTIVE.key ? 'Ngưng hoạt động' : 'Hoạt động lại'}
              />
            </Popconfirm>
          </>
        )
      }
    }
  ]

  return (
    <BaseView>
      <BaseFilter onFilter={handleFilter} onReset={handleFilterReset} isLoading={isFetching} filters={filterFields}></BaseFilter>
      <Row gutter={16}>
        <Col span={24} style={{ marginTop: 16 }}>
          <BaseTable size='small' columns={columns} data={data} total={0} isLoading={isFetching} onPageChange={handlePageChange} />
        </Col>
      </Row>
    </BaseView>
  )
}

export default SubscriptionPlanView
