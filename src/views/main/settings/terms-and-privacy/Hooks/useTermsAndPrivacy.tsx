import { useInfiniteQuery, useMutation } from '@tanstack/react-query'
import { useEffect, useState } from 'react'
import { ISubscriptionPlan, ISubscriptionPlanFilter, ISubscriptionPlanResponse } from '~/dto/subscriptionPlan.dto'
import { ITermsAndPrivacy, ITermsAndPrivacyFilter, ITermsAndPrivacyResponse } from '~/dto/termAndPrivacy'
import { toastService } from '~/services'
import { subscriptionPlanService } from '~/services/SubsciptionPlanService/subscriptionPlanService'
import { termsAndPrivacyService } from '~/services/TermAndPrivacyService/termAndPrivacyService'

export const useTermsAndPrivacy = (enabled: boolean = false) => {
  const [filter, setFilter] = useState<ITermsAndPrivacyFilter>({})
  const { data, isFetching, refetch } = useInfiniteQuery<ITermsAndPrivacyResponse>({
    queryKey: [termsAndPrivacyService.APIs.LIST],
    queryFn: async () => {
      return await termsAndPrivacyService.pagination(filter)
    },
    getNextPageParam: (lastPage, allPages) => {
      if (lastPage.data.length === 0) return undefined
      return allPages.length + 1
    },
    initialPageParam: 1,
    enabled: enabled
  })

  const setActive = async (id: string) => {
    return await termsAndPrivacyService.setActive(id).then(() => {
      refetch()
    })
  }

  const setInactive = async (id: string) => {
    return await termsAndPrivacyService.setInactive(id).then(() => {
      refetch()
    })
  }

  const useUpdate = () => {
    return useMutation({
      mutationFn: async (data: ITermsAndPrivacy) => {
        return await termsAndPrivacyService.update(data)
      },
      onSuccess: () => {
        toastService.success('Cập nhật thông tin gói dịch vụ thành công')
      },
      onError: (error) => {
        toastService.error(error.message)
      }
    })
  }

  const useCreate = () => {
    return useMutation({
      mutationFn: async (data: ITermsAndPrivacy) => {
        return termsAndPrivacyService.create(data)
      },
      onSuccess: () => {
        refetch()
        toastService.success('Tạo gói dịch vụ thành công')
      },
      onError: (error) => {
        toastService.error(error.message)
      }
    })
  }
  const formattedData = data?.pages.flatMap((page) => page.data)
  const total = data?.pages[0].total

  return {
    data: formattedData,
    total,
    isFetching,
    loadData: refetch,
    setFilter,
    useUpdate,
    useCreate,
    setActive,
    setInactive
  }
}
