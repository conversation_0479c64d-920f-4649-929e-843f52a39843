import { useState } from 'react'
import { enumData } from '~/common/enums/enumData'
import { IFilter } from '~/components/BaseFilter/BaseFilter'

interface IProps {}
export const useTermsAndPrivacyFilterConfig = (iProps?: IProps) => {
  const handleFilter = (values: any) => {
    setFilterData(values)
  }
  const handleFilterReset = () => {
    setFilterData({})
  }

  const [filterData, setFilterData] = useState({})
  const [filterFields, setFilterFields] = useState<IFilter[]>([
    {
      key: 'title',
      name: 'Tiêu đề',
      type: enumData.FILTER_TYPE.INPUT.key
    },

    //trạng thái
    {
      key: 'status',
      name: 'Trạng thái',
      type: enumData.FILTER_TYPE.SELECT.key,
      selectOptions: Object.values(enumData.TERMS_AND_PRIVACY_STATUS).map((status: any) => {
        return {
          key: status.key,
          value: status.value
        }
      })
    }
  ])

  return {
    filterFields,
    filterData,
    setFilterData,
    handleFilter,
    handleFilterReset
  }
}
