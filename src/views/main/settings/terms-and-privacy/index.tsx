import { ColumnsType } from 'antd/es/table'
import { FC, useEffect, useState } from 'react'
import BaseTable from '~/components/BaseTable'
import BaseView from '~/components/BaseView'
import { ITermsAndPrivacy } from '~/dto/termAndPrivacy'
import EditButton from './component/EditButton'
import { useTermsAndPrivacy } from './Hooks/useTermsAndPrivacy'
import moment from 'moment'
import { Popconfirm, Tag } from 'antd'
import { enumData } from '~/common/enums/enumData'
import BaseButton from '~/components/BaseButton'
import { DeleteOutlined, PlayCircleOutlined, StopOutlined } from '@ant-design/icons'
import BaseFilter from '~/components/BaseFilter/BaseFilter'
import { useTermsAndPrivacyFilterConfig } from './Hooks/useTermFilterConfig'

const TermsAndPrivacyView: FC<any> = function () {
  const { data, total, loadData, setFilter, setInactive, setActive } = useTermsAndPrivacy(true)
  const { filterFields, filterData, handleFilter, handleFilterReset } = useTermsAndPrivacyFilterConfig()

  const handlePageChange = (newPageIndex: number, newPageSize: number) => {
    setFilter({ pageIndex: newPageIndex, pageSize: newPageSize })
  }

  useEffect(() => {
    setFilter(filterData)
  }, [filterData])

  const columns: ColumnsType<ITermsAndPrivacy> = [
    {
      title: 'Tiêu đề',
      dataIndex: 'title',
      key: 'title',
      align: 'center'
    },
    {
      title: 'Thời gian cập nhật',
      dataIndex: 'updatedDate',
      align: 'center',
      key: 'updatedDate',
      render: (value) => (value ? moment(value).format('DD/MM/YYYY HH:mm:ss') : '')
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      align: 'center',
      render: (value) => (
        <Tag style={{ fontSize: 14, padding: 5 }} color={enumData.TERMS_AND_PRIVACY_STATUS[value]?.color}>
          {enumData.TERMS_AND_PRIVACY_STATUS[value]?.value}
        </Tag>
      )
    },
    {
      title: 'Tác vụ',
      key: 'action',
      align: 'center',
      render: (record) => (
        <>
          <EditButton data={record} onClose={loadData} />
          <Popconfirm
            title={
              record.status === enumData.TERMS_AND_PRIVACY_STATUS.ACTIVE.key
                ? 'Bạn có chắc chắn muốn ngưng hoạt động không?'
                : 'Bạn có chắc chắn muốn cho hoạt động lại không?'
            }
            onConfirm={() => {
              record.status === enumData.TERMS_AND_PRIVACY_STATUS.ACTIVE.key ? setInactive(record.id) : setActive(record.id)
            }}>
            <BaseButton
              onClick={() => {}}
              type='default'
              danger={record.status === enumData.TERMS_AND_PRIVACY_STATUS.ACTIVE.key}
              icon={record.status === enumData.TERMS_AND_PRIVACY_STATUS.ACTIVE.key ? <StopOutlined /> : <PlayCircleOutlined />}
              tooltip={record.status === enumData.TERMS_AND_PRIVACY_STATUS.ACTIVE.key ? 'Ngưng hoạt động' : 'Hoạt động lại'}
            />
          </Popconfirm>
        </>
      )
    }
  ]

  return (
    <BaseView>
      <BaseFilter onFilter={handleFilter} onReset={handleFilterReset} isLoading={false} filters={filterFields}></BaseFilter>
      <BaseTable columns={columns} data={data} total={total} isLoading={false} onPageChange={handlePageChange} />
    </BaseView>
  )
}

export default TermsAndPrivacyView
