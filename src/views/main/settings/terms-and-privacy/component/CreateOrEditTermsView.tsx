import { SaveOutlined } from '@ant-design/icons'
import { Button, Col, Form, Input, Row, Spin, Upload, message } from 'antd'
import { useForm } from 'antd/es/form/Form'
import TextArea from 'antd/es/input/TextArea'
import { FC, useEffect } from 'react'
import BaseModal from '~/components/BaseModal'
import ReactQuill from 'react-quill'
import { useTermsAndPrivacy } from '../Hooks/useTermsAndPrivacy'
import { ITermsAndPrivacy } from '~/dto/termAndPrivacy'

interface IProps {
  data?: ITermsAndPrivacy
  open: boolean
  onClose: () => void
}
export const CreateOrEditTermsView: FC<IProps> = ({ data, open, onClose }: IProps) => {
  const { loadData, useUpdate, useCreate } = useTermsAndPrivacy()
  const { mutateAsync: updateData, isPending: isUpdating } = useUpdate()
  const { mutateAsync: createData, isPending: isCreating } = useCreate()
  const [form] = useForm<ITermsAndPrivacy>()

  useEffect(() => {
    if (open && data) {
      form.setFieldsValue({
        ...data
      })
    } else {
      form.resetFields()
    }
  }, [open, data, form])

  const handleSave = async (values: ITermsAndPrivacy) => {
    if (data && data != null) {
      const modifiedValues = {
        ...data,
        ...values
      }
      await updateData(modifiedValues)
    } else {
      await createData(values).then(() => {
        handleCancel()
      })
    }
  }

  const handleCancel = async () => {
    onClose()
  }

  const modalContent = (
    <Spin spinning={data != null ? isUpdating : isCreating}>
      <Form form={form} layout='vertical' onFinish={handleSave}>
        <Row gutter={24}>
          <Col span={12}>
            <Form.Item label='Tiêu đề' name='title' rules={[{ required: true, message: 'Vui lòng nhập tiêu đề!' }]}>
              <Input placeholder='Nhập tiêu đề' />
            </Form.Item>
          </Col>

          <Col span={24}>
            <Form.Item label='Nội dung' name='contentHtml' rules={[{ required: true, message: 'Vui lòng nhập nội dung!' }]}>
              {/* <TextArea rows={10} placeholder='Nhập nội dung' /> */}
              <ReactQuill theme='snow' style={{ height: '300px' }} />
            </Form.Item>
          </Col>
        </Row>
        <Row
          style={{
            marginTop: 24,
            borderTop: '1px solid #f0f0f0',
            paddingTop: 16
          }}
          justify={'center'}>
          <Button onClick={handleCancel} style={{ marginRight: 8 }}>
            Hủy
          </Button>
          <Button type='primary' htmlType='submit' icon={<SaveOutlined />}>
            {data ? 'Cập nhật' : 'Tạo mới'}
          </Button>
        </Row>
      </Form>
    </Spin>
  )
  return (
    <BaseModal
      open={open}
      onClose={handleCancel}
      title={data ? 'Chỉnh sửa thông tin' : 'Tạo mới thông tin'}
      description={data ? 'Chỉnh sửa thông tin' : 'Tạo mới thông tin'}
      childrenBody={modalContent}
    />
  )
}
