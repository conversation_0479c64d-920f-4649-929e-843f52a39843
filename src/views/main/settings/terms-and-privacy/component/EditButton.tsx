import { EditOutlined } from '@ant-design/icons'
import { useState } from 'react'
import { BaseButton } from '~/components'
import { ITermsAndPrivacy } from '~/dto/termAndPrivacy'
import { CreateOrEditTermsView } from './CreateOrEditTermsView'

interface EditButtonProps {
  data: ITermsAndPrivacy
  isLoading?: boolean
  onClose?: () => void
}

const EditButton = ({ data, isLoading = false, onClose }: EditButtonProps) => {
  const [open, setOpen] = useState(false)
  const clickOpen = () => {
    setOpen(true)
  }

  const clickClose = async () => {
    setOpen(false)
    onClose && onClose()
  }

  return (
    <>
      <BaseButton icon={<EditOutlined />} onClick={clickOpen} type='primary' tooltip={'Chỉnh sửa điều khoản'} />
      <CreateOrEditTermsView data={data} open={open} onClose={clickClose} />
    </>
  )
}

export default EditButton
