import BaseView from '~/components/BaseView'
import { <PERSON><PERSON>, Card, Col, Row, Form, Input, Spin, Drawer, Select } from 'antd'
import { EyeOutlined, SaveOutlined } from '@ant-design/icons'
import { useApiDocument } from '../Hooks/useApiDocument'
import ReactQuill from 'react-quill'
import { useEffect, useState } from 'react'
import { IApiDocContentCreate } from '~/dto/apiDocumentContent'
import { useForm } from 'antd/es/form/Form'
import { useRef } from 'react'
import { useLocation, useNavigate, useParams } from 'react-router-dom'
import { cleanHtml } from '~/common/helper/helper'

export const EditView = () => {
  const navigate = useNavigate()
  const { id } = useParams()
  const { state } = useLocation()
  const { createMutation, uploadImageMutation, updateMutation } = useApiDocument()
  const [form] = useForm<IApiDocContentCreate>()
  const [previewData, setPreviewData] = useState<{
    title: string
    slug: string
    content: string
  } | null>(null)
  const [drawerVisible, setDrawerVisible] = useState(false)
  const quillRef = useRef<ReactQuill>(null)

  const handleSave = async (values: IApiDocContentCreate) => {
    try {
      await updateMutation.mutateAsync({
        ...values,
        content: cleanHtml(values.content),
        id
      })
      navigate('/settings/setting-documents')
      form.resetFields()
      setPreviewData(null)
    } catch (error) {
      console.error('Error creating document:', error)
    }
  }

  const handleView = () => {
    const formValues = form.getFieldsValue()
    setPreviewData({
      title: formValues.title || '',
      slug: formValues.slug || '',
      content: formValues.content || ''
    })
    setDrawerVisible(true)
  }

  const handleImageUpload = async (quill: any) => {
    const input = document.createElement('input')
    input.setAttribute('type', 'file')
    input.setAttribute('accept', 'image/*')
    input.click()

    input.onchange = async () => {
      const file = input.files?.[0]
      if (!file) return

      try {
        const formData = new FormData()
        formData.append('file', file)

        // Gọi mutation để upload
        const response = await uploadImageMutation.mutateAsync(formData)

        // Lấy đường dẫn ảnh từ trường Location trong response
        const imageUrl = response?.Location
        if (!imageUrl) throw new Error('Không tìm thấy URL ảnh trong response')

        // Chèn ảnh vào editor
        const range = quill.getSelection()
        quill.insertEmbed(range.index, 'image', imageUrl)
        quill.setSelection(range.index + 1)
      } catch (error) {
        console.error('Image upload failed:', error)
      }
    }
  }

  const modules = {
    toolbar: [
      [{ font: [] }],
      [{ header: [1, 2, 3, 4, 5, 6, false] }],
      ['bold', 'italic', 'underline', 'strike'],
      [{ color: [] }, { background: [] }],
      [{ script: 'sub' }, { script: 'super' }],
      [{ list: 'ordered' }, { list: 'bullet' }],
      [{ indent: '-1' }, { indent: '+1' }],
      [{ align: [] }],
      ['blockquote', 'code-block'],
      ['link', 'image', 'video'],
      ['clean']
    ]
  }

  const decodeHtmlEntities = (str: string) => {
    const textarea = document.createElement('textarea')
    textarea.innerHTML = str
    return textarea.value
  }

  useEffect(() => {
    if (!quillRef.current) return
    const quill = quillRef.current.getEditor()

    // Override handler "image"
    const toolbar = quill.getModule('toolbar')
    toolbar.addHandler('image', () => handleImageUpload(quill))
  }, [])

  return (
    <BaseView>
      <Card
        title='Cập nhật tài liệu'
        extra={
          <Row>
            <Col span={12} style={{ textAlign: 'left', display: 'flex', gap: 8 }}>
              <Button type='primary' onClick={() => handleSave(form.getFieldsValue())} icon={<SaveOutlined />}>
                Save
              </Button>
              <Button type='default' icon={<EyeOutlined />} onClick={handleView}>
                View
              </Button>
            </Col>
          </Row>
        }>
        <Spin spinning={updateMutation.isPending}>
          <Form initialValues={state?.record} form={form} layout='vertical'>
            <Row gutter={24}>
              {/* Title */}
              <Col span={10}>
                <Form.Item label='Tiêu đề' name='title' rules={[{ required: true, message: 'Vui lòng nhập tiêu đề!' }]}>
                  <Input placeholder='Nhập tiêu đề tài liệu' />
                </Form.Item>
              </Col>
              {/* Slug */}
              <Col span={10}>
                <Form.Item label='Slug' name='slug'>
                  <Input placeholder='Nhập slug (tùy chọn)' />
                </Form.Item>
              </Col>
              <Col span={4}>
                <Form.Item label='Locale' name='locale' rules={[{ required: true, message: 'Vui lòng chọn ngôn ngữ!' }]}>
                  <Select>
                    <Select.Option value='en'>English</Select.Option>
                    <Select.Option value='vn'>Vietnamese</Select.Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col span={24}>
                <Form.Item label='Nội dung' name='content' rules={[{ required: true, message: 'Vui lòng nhập nội dung!' }]}>
                  <ReactQuill ref={quillRef} theme='snow' style={{ height: '500px' }} modules={modules} placeholder='Nhập nội dung tài liệu...' />
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </Spin>
      </Card>
      <Drawer title='Preview' placement='right' width={1000} open={drawerVisible} onClose={() => setDrawerVisible(false)} destroyOnClose>
        {previewData && (
          <div>
            <h2 style={{ marginBottom: 16, fontSize: '24px', fontWeight: 'bold' }}>{previewData.title}</h2>
            <div
              className='ql-editor'
              dangerouslySetInnerHTML={{ __html: decodeHtmlEntities(previewData.content) }}
              style={{
                border: '1px solid #f0f0f0',
                padding: '24px',
                borderRadius: '8px',
                backgroundColor: '#fafafa',
                minHeight: '400px'
              }}
            />
          </div>
        )}
      </Drawer>
    </BaseView>
  )
}
