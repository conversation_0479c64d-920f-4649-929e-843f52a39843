import BaseView from '~/components/BaseView'
import { useApiDocument } from './Hooks/useApiDocument'
import BaseTable from '~/components/BaseTable'
import moment from 'moment'
import { Outlet, useNavigate } from 'react-router-dom'
import { Button, Row } from 'antd'
import { ColumnsType } from 'antd/es/table'

export const ApiDocumentView = () => {
  const { data, total, isFetching } = useApiDocument(true)
  const navigate = useNavigate()

  const columns: ColumnsType<any> = [
    {
      title: 'STT',
      dataIndex: 'sort',
      key: 'sort',
      align: 'center'
    },
    {
      title: 'Tiêu đề',
      dataIndex: 'title',
      key: 'title'
    },
    {
      title: 'Slug',
      dataIndex: 'slug',
      key: 'slug'
    },
    // content
    {
      title: 'Nội dung',
      dataIndex: 'content',
      key: 'content',
      render: (value: string) => (value?.length > 50 ? `${value.slice(0, 50)}...` : value)
    },
    {
      title: 'Ngày tạo',
      dataIndex: 'createdDate',
      key: 'createdDate',

      render: (value) => (value ? moment(value).format('DD/MM/YYYY HH:mm:ss') : '')
    },
    {
      title: 'Ngày cập nhật',
      dataIndex: 'updatedDate',
      key: 'updatedDate',
      render: (value) => (value ? moment(value).format('DD/MM/YYYY HH:mm:ss') : '')
    },
    {
      title: 'Tác vụ',
      key: 'action',
      render: (record) => {
        return (
          <>
            <Button type='primary' onClick={() => handleNavigateEdit(record)}>
              Edit
            </Button>
          </>
        )
      }
    }
  ]

  const handleNavigateCreate = () => {
    navigate('/settings/setting-documents/create')
  }

  const handleNavigateEdit = (record: any) => {
    navigate(`/settings/setting-documents/${record.id}/edit`, { state: { record } })
  }

  return (
    <BaseView>
      <Row justify='end' style={{ marginBottom: 16 }}>
        <Button type='primary' onClick={handleNavigateCreate}>
          Create
        </Button>
      </Row>

      <BaseTable columns={columns} data={data} total={total} isLoading={isFetching} />
    </BaseView>
  )
}
