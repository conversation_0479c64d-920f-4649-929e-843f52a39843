import { useInfiniteQuery, useMutation } from '@tanstack/react-query'
import { useState } from 'react'
import { IPaymentTransaction, IPaymentTransactionFilter } from '~/dto/paymentTransaction.dto'
import { toastService } from '~/services'
import { advisoryService } from '~/services/AdvisoryService/advisoryService'
export const useAdvisory = (enabled: boolean = false) => {
  const [filter, setFilter] = useState<IPaymentTransactionFilter>({})
  const { data, isFetching, refetch } = useInfiniteQuery({
    queryKey: [advisoryService.APIs.LIST, filter],
    queryFn: async () => {
      return await advisoryService.getPagination(filter)
    },
    getNextPageParam: (lastPage, allPages) => {
      if (lastPage.data.length === 0) return undefined
      return allPages.length + 1
    },
    initialPageParam: 1,
    enabled: enabled
  })

  const useUpdateStatus = () => {
    return useMutation({
      mutationFn: async (body: { id: string; status: string }) => {
        return await advisoryService.update(body)
      },
      onSuccess: () => {
        refetch()
        toastService.success('Cập nhật trạng thái thành công')
      },
      onError: () => {
        toastService.error('Cập nhật trạng thái thất bại')
      }
    })
  }

  const getDetail = async (id: string): Promise<IPaymentTransaction> => {
    return await advisoryService.getDetail(id)
  }

  const formattedData = data?.pages.flatMap((page) => page.data)
  const total = data?.pages[0].total

  return {
    data: formattedData,
    total: total,
    isLoading: isFetching,
    loadData: refetch,
    setFilter,
    getDetail,
    useUpdateStatus
  }
}
