import { useState } from 'react'
import { enumData } from '~/common/enums/enumData'
import { IFilter } from '~/components/BaseFilter/BaseFilter'
import { getDateRangeClosed } from '~/common/helper'
import moment from 'moment'

interface IProps {}
export const useAdvisoryFilterConfig = (iProps?: IProps) => {
  const handleFilter = (values: any) => {
    if (values.createdDate) {
      values.createdDateFrom = moment(new Date(values.createdDate[0]).setHours(0, 0, 0)).format('YYYY-MM-DD HH:mm:ss')
      values.createdDateTo = moment(new Date(values.createdDate[1]).setHours(23, 59, 59)).format('YYYY-MM-DD HH:mm:ss')
      delete values.createdDate
    }
    setFilterData(values)
  }
  const handleFilterReset = () => {
    setFilterData({})
  }

  const [filterData, setFilterData] = useState({})
  const [filterFields, setFilterFields] = useState<IFilter[]>([
    {
      key: 'fullName',
      name: 'Tên khách hàng',
      type: enumData.FILTER_TYPE.INPUT.key
    },

    {
      key: 'email',
      name: 'Email khách hàng',
      type: enumData.FILTER_TYPE.INPUT.key
    },
    //date
    {
      key: 'createdDate',
      name: 'Ngày gửi',
      type: enumData.FILTER_TYPE.DATE_RANGE.key
    },
    //trạng thái
    {
      key: 'status',
      name: 'Trạng thái',
      type: enumData.FILTER_TYPE.SELECT.key,
      selectOptions: Object.values(enumData.ADVISORY_STATUS).map((status: any) => {
        return {
          key: status.key,
          value: status.value
        }
      })
    }
  ])

  return {
    filterFields,
    filterData,
    setFilterData,
    handleFilter,
    handleFilterReset
  }
}
