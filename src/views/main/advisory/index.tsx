import { FC, useEffect } from 'react'
import { Col, Popconfirm, Row, Tag } from 'antd'
import type { ColumnsType } from 'antd/es/table'
import BaseView from '~/components/BaseView'
import BaseTable from '~/components/BaseTable'
import { IPaymentTransaction, IPaymentTransactionFilter } from '~/dto/paymentTransaction.dto'
import BaseFilter, { IFilter } from '~/components/BaseFilter/BaseFilter'

import DetailButton from './component/DetailButton'
import moment from 'moment'
import { enumData } from '~/common/enums/enumData'
import { useAdvisoryFilterConfig } from './Hooks/useAvisoryFilterConfig'
import { useAdvisory } from './Hooks/useAvisory'
import { IAdvisory } from '~/dto/advisory.dto'
import { BaseButton } from '~/components'
import { CheckCircleOutlined } from '@ant-design/icons'

type IProps = {}

const AdvisoryView: FC<IProps> = () => {
  const { data, total, isLoading, useUpdateStatus, setFilter } = useAdvisory(true)
  const { mutateAsync: updateStatus } = useUpdateStatus()

  const { filterData, filterFields, handleFilter, handleFilterReset, setFilterData } = useAdvisoryFilterConfig()
  // const { data, isLoading, total } = useListCustomer()

  useEffect(() => {
    setFilter(filterData)
  }, [filterData])

  const handlePageChange = (newPageIndex: number, newPageSize: number) => {
    setFilterData({
      ...filterData,
      pageIndex: newPageIndex,
      pageSize: newPageSize
    })
  }

  const columns: ColumnsType<IAdvisory> = [
    //Bảng: Stt, Tên khách hàng, Email, Nội dung, Ngày gửi, Trạng thái (Đã đọc/Chưa đọc), Tác vụ (Đánh dấu đã đọc)
    {
      title: 'STT',
      key: 'stt',
      width: 60,
      align: 'center',
      render: (_, __, index) => index + 1
    },
    {
      title: 'Tên khách hàng',
      dataIndex: 'fullName',
      key: 'fullName',
      align: 'center'
    },
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email',
      align: 'center'
    },
    //chủ đề
    {
      title: 'Nội dụng',
      dataIndex: 'message',
      key: 'message',
      align: 'center'
    },
    {
      title: 'Ngày gửi',
      dataIndex: 'createdDate',
      key: 'createdDate',
      align: 'center',
      render: (value: any) => moment(value).format('DD/MM/YYYY')
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      align: 'center',
      render: (value: any) => <Tag color={enumData.ADVISORY_STATUS[value]?.color}>{enumData.ADVISORY_STATUS[value]?.value}</Tag>
    },
    {
      title: 'Tác vụ',
      key: 'action',
      render: (record) => {
        if (record.status === enumData.ADVISORY_STATUS.DONE.key) return null
        return (
          <Popconfirm title='Bạn có chắc chắn muốn đánh dấu đã đọc?' onConfirm={() => handleRead(record)}>
            <BaseButton icon={<CheckCircleOutlined />} onClick={() => {}} type='primary'></BaseButton>
          </Popconfirm>
        )
      },
      align: 'center'
    }
  ]

  const handleRead = (record: IAdvisory) => {
    //call api update status
    //refetch
    updateStatus({ id: record.id, status: enumData.ADVISORY_STATUS.DONE.key })
  }

  return (
    <BaseView>
      <BaseFilter onFilter={handleFilter} onReset={handleFilterReset} isLoading={isLoading} filters={filterFields}></BaseFilter>
      <Row gutter={16}>
        <Col span={24} style={{ marginTop: 16 }}>
          <BaseTable columns={columns} data={data} total={total} isLoading={isLoading} onPageChange={handlePageChange} />
        </Col>
      </Row>
    </BaseView>
  )
}

export default AdvisoryView
