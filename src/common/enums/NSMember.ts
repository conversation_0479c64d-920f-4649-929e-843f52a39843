export namespace NSMember {
  export enum EMemberType {
    ADMIN = 'ADMIN',
    Member = 'Member'
  }
  export enum EMemberStatus {
    ACTIVE = 'ACTIVE',
    INACTIVE = 'INACTIVE',
    LOCKED = 'LOCKED'
  }
  export enum EMemberValidateStatus {
    VALIDATED = 'VALIDATED',
    INVALIDATED = 'INVALIDATED'
  }
}

export const EMember = {
  EMemberStatus: {
    ACTIVE: {
      label: 'Active',
      value: NSMember.EMemberStatus.ACTIVE,
      name: 'Hoạt động',
      color: 'green'
    },
    INACTIVE: {
      label: 'Inactive',
      value: NSMember.EMemberStatus.INACTIVE,
      name: 'Không hoạt động',
      color: 'red'
    },
    LOCKED: {
      label: 'Locked',
      value: NSMember.EMemberStatus.LOCKED,
      name: 'Đã khóa',
      color: 'orange'
    }
  },

  EMemberValidateStatus: {
    VALIDATED: {
      label: 'Validated',
      value: NSMember.EMemberValidateStatus.VALIDATED,
      name: 'Đã xác thực',
      color: 'blue'
    },
    INVALIDATED: {
      label: 'Invalidated',
      value: NSMember.EMemberValidateStatus.INVALIDATED,
      name: 'Chưa xác thực',
      color: 'red'
    }
  }
}
