export namespace NSProduct {
  export enum EProductType {
    PERSONAL = "PERSONAL",
    ENTERPRISE = "ENTERPRISE",
  }
  export enum EProductStatus {
    ACTIVE = "ACTIVE",
    INACTIVE = "INACTIVE",
  }
}

export const EProduct = {
  EProductType: {
    PERSONAL: {
      label: "Personal",
      value: NSProduct.EProductType.PERSONAL,
      name: "Cá nhân",
      color: "blue",
    },
    ENTERPRISE: {
      label: "Enterprise",
      value: NSProduct.EProductType.ENTERPRISE,
      name: "<PERSON><PERSON><PERSON> nghiệ<PERSON>",
      color: "green",
    },
  },
  EProductStatus: {
    ACTIVE: {
      label: "Active",
      code: "ACTIVE",
      value: NSProduct.EProductStatus.ACTIVE,
      name: "Hoạt động",
      color: "green",
    },
    INACTIVE: {
      label: "Inactive",
      code: "INACTIVE",
      value: NSProduct.EProductStatus.INACTIVE,
      name: "<PERSON><PERSON>ông hoạt động",
      color: "red",
    },
  },
};
