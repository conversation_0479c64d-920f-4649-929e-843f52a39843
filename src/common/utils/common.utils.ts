const getKeyEnumByValue = <T = any>(targetEnum: T, valueFind: any) => {
  return Object.keys(targetEnum)[Object.values(targetEnum).indexOf(valueFind)] || ''
}

const getColorPercent = (percent: number, reverse = false) => {
  if (reverse) {
    if (percent <= 50) return 'green'
    if (percent <= 100) return 'orange'
    return 'red'
  }
  if (percent >= 0 && percent <= 50) return 'red'
  if (percent >= 100) return 'green'
  if (percent > 50) return 'orange'
  return 'red'
}

const getColorStatus = (status: string) => {
  switch (status) {
    case 'DONE':
      return 'green'
    case 'DOING':
      return 'orange'
    case 'PENDING':
      return 'blue'
    case 'FAILED':
      return 'red'
    case 'SENT':
      return 'green'
    case 'SIGNED':
      return 'orange'
    case 'DRAFT':
      return 'blue'
    default:
      return 'default'
  }
}

const getColorStatus_vn = (status: string) => {
  const lowerCase = status.toLowerCase()
  switch (lowerCase) {
    case 'hoàn thành':
      return 'green'
    case 'đang thực hiện':
      return 'orange'
    case 'chờ xử lý':
      return 'blue'
    case 'thất bại':
      return 'red'
    case 'đã gửi':
      return 'green'
    case 'đã ký':
      return 'orange'
    case 'nháp':
      return 'blue'
    default:
      return 'default'
  }
}

// 👉 Custom plugin hiển thị giá trị
const showValueOnBar = {
  id: 'showValueOnBar',
  afterDatasetsDraw(chart: any) {
    const { ctx } = chart
    chart.data.datasets.forEach((dataset: any, datasetIndex: number) => {
      const meta = chart.getDatasetMeta(datasetIndex)
      meta.data.forEach((bar: any, index: number) => {
        const value = dataset.data[index]
        ctx.save()
        ctx.fillStyle = '#333'
        ctx.font = 'bold 12px sans-serif'
        ctx.textAlign = 'center'
        ctx.fillText(value, bar.x, bar.y - 8)
        ctx.restore()
      })
    })
  }
}

export { getKeyEnumByValue, getColorPercent, getColorStatus, showValueOnBar }
