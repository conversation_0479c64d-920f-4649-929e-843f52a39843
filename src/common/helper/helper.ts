import type { RcFile } from "antd/es/upload";
import dayjs from "dayjs";
import moment from "moment";

// Format number theo locale
export const formatNumber = (number: string): string => {
  const numberInt = parseFloat(number);
  let lang = localStorage.getItem("lang") || "en";

  if (Number.isNaN(numberInt)) {
    return "0";
  }

  if (lang === "vi") {
    lang = "vi";
  }

  return numberInt.toLocaleString(lang);
};

// Format date theo định dạng Việt Nam
export const formatDateCustom = (
  date: string,
  format: string = "DD/MM/YYYY HH:mm"
): string => {
  return dayjs(date).format(format);
};

// Tính số ngày còn lại
export const daysRemaining = (date: string): number | null => {
  return date
    ? Math.ceil(
      (new Date(date).getTime() - new Date().getTime()) /
      (1000 * 60 * 60 * 24)
    )
    : null;
};

// Chuyển đổi number thành string có dấu phẩy
export const numberToString = (val: number): string => {
  return `${val}`.replace(/\B(?=(\d{3})+(?!\d))/g, ",");
};

// Chuyển đổi string thành number (remove các ký tự format)
export const stringToNumber = (val: string): number => {
  return Number(`${val || "0"}`.replace(/\$\s?|(,*)/g, ""));
};

// Convert file thành base64 string
export const getBase64 = (file: RcFile): Promise<string> =>
  new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = (error) => reject(error);
  });

// Rút gọn text dài với format "start...end"
export const pipeLongTextUi = (
  value: string = "",
  leftCharAmount: number = 4,
  rightCharAmount: number = 4
): string => {
  if (value.length <= leftCharAmount + rightCharAmount + 3) {
    return value;
  }
  return `${value?.substring(0, leftCharAmount) ?? ""}...${value?.substring(value.length - rightCharAmount) ?? ""
    }`;
};

// Format price với dấu phẩy
export const formatPrice = (price: string): string => {
  if (!price) {
    return "0";
  }

  if (!`${price}`.includes(".")) {
    return `${price}`.replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  }

  const separatorList = `${price}`.split(".");
  const intPart = separatorList[0].replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  const decimalPart = separatorList[1] || "";
  return `${intPart}.${decimalPart}`;
};

// Format currency với symbol
export const formatCurrency = (
  amount: string | number,
  currency: "USD" | "VND" = "USD"
): string => {
  const numAmount =
    typeof amount === "string" ? stringToNumber(amount) : amount;

  if (currency === "VND") {
    return `${numberToString(numAmount)} VND`;
  }

  return `$${numberToString(numAmount)}`;
};

// Format date theo định dạng Việt Nam
export const formatDate = (
  dateString: string,
  format: string = "DD/MM/YYYY",
  includeTime: boolean = true
): string => {
  if (!dateString) return "N/A";

  try {
    const date = new Date(dateString);
    const options: Intl.DateTimeFormatOptions = {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    };

    if (includeTime) {
      options.hour = "2-digit";
      options.minute = "2-digit";
      options.hour12 = false;
    }

    const formattedDate = date.toLocaleDateString("vi-VN", options);

    return moment(formattedDate).format(format);
  } catch {
    return dateString;
  }
};

// Format date theo định dạng Việt Nam
export function formatMoneyVND(money: string | number) {
  if (!!money || money === 0) {
    if (money && money.toString().length > 0) {
      return (
        money.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1.") + " VNĐ"
      );
    } else {
      return money + " VNĐ";
    }
  } else {
    return "0 VNĐ";
  }
}

// Format money VND với dấu phẩy
export function formatMoneyVNDWithComma(value: string | number): string {
  if (!value && value !== 0) return null;

  const strValue = value.toString();
  return strValue.replace(/\B(?=(\d{3})+(?!\d))/g, ".");
}

// Format number với dấu phẩy (tái sử dụng regex)
export function addCommaToNumber(value: string | number): string {
  if (!value && value !== 0) return "0";

  const strValue = value.toString();
  return strValue.replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

// Format number với dấu phẩy và suffix tùy chọn
export function formatNumberWithComma(
  value: string | number,
  suffix: string = ""
): string {
  if (!value && value !== 0) return `0${suffix ? " " + suffix : ""}`;

  const strValue = value.toString();
  const formatted = strValue.replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  return suffix ? `${formatted} ${suffix}` : formatted;
}

// Truncate text với suffix
export const truncateText = (text: string, maxLength: number = 100): string => {
  if (!text || text.length <= maxLength) return text;
  return text.substring(0, maxLength) + "...";
};

// Validate email format
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// Validate phone number (Vietnam format)
export const isValidPhone = (phone: string): boolean => {
  const phoneRegex = /^(\+84|84|0)[3-9]\d{8}$/;
  return phoneRegex.test(phone.replace(/\s+/g, ""));
};

// Generate random string
export const generateRandomString = (length: number = 8): string => {
  const chars =
    "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
  let result = "";
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
};

// Copy text to clipboard
export const copyToClipboard = async (text: string): Promise<boolean> => {
  try {
    await navigator.clipboard.writeText(text);
    return true;
  } catch {
    // Fallback cho browsers cũ
    const textArea = document.createElement("textarea");
    textArea.value = text;
    document.body.appendChild(textArea);
    textArea.select();
    document.body.removeChild(textArea);
    return true;
  }
};

export const cleanHtml = (html: string): string => {
  return html
    .replace(/<p>(&nbsp;|\s|<br>|)*<\/p>/gi, '') // Xóa các thẻ p rỗng hoặc chỉ chứa khoảng trắng
    .replace(/<p><\/p>/gi, '') // Xóa thẻ p rỗng
    .replace(/&nbsp;/gi, '') // Xóa khoảng trắng không phá dòng
    .trim()
}

const helper = {
  formatNumber,
  numberToString,
  stringToNumber,
  getBase64,
  pipeLongTextUi,
  formatPrice,
  formatCurrency,
  formatDate,
  formatMoneyVND,
  formatMoneyVNDWithComma,
  addCommaToNumber,
  formatNumberWithComma,
  truncateText,
  isValidEmail,
  isValidPhone,
  generateRandomString,
  copyToClipboard,
  formatDateCustom,
  daysRemaining,
};

export default helper;
