import dayjs, { Dayjs } from 'dayjs'
import utc from 'dayjs/plugin/utc'
import timezone from 'dayjs/plugin/timezone'
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter'
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore'
import { configEnv } from '~/@config'

dayjs.extend(utc)
dayjs.extend(timezone)
dayjs.extend(isSameOrAfter)
dayjs.extend(isSameOrBefore)

export { dayjs, Dayjs }

/**
 * 🕒 Chuẩn hóa `start` về dạng ISO UTC
 * - Với mode = 'date': về 00:00:00
 * - Với mode = 'datetime': giữ nguyên giờ phút
 */
export function normalizeStartDay(
  startDate?: Dayjs | string | null,
  mode: 'date' | 'datetime' = 'date'
): string | undefined {
  if (!startDate) return
  const startDay = typeof startDate === 'string' ? dayjs(startDate) : startDate
  return mode === 'date'
    ? startDay.startOf('day').utc().toISOString()
    : startDay.utc().toISOString()
}

/**
 * 🕓 Chuẩn hóa `end` về dạng ISO UTC
 * - Với mode = 'date': về 23:59:59.999
 * - Với mode = 'datetime': giữ nguyên giờ phút
 */
export function normalizeEndDay(
  endDate?: Dayjs | string | null,
  mode: 'date' | 'datetime' = 'date'
): string | undefined {
  if (!endDate) return
  const endDay = typeof endDate === 'string' ? dayjs(endDate) : endDate
  return mode === 'date'
    ? endDay.endOf('day').utc().toISOString()
    : endDay.utc().toISOString()
}

/**
 * 🎯 Dùng khi lấy khoảng [from → to], bao gồm cả ngày `to`
 * - BE dùng `Between(from, to)`
 */
export function getDateRangeClosed(
  dateRange: [Dayjs | null, Dayjs | null] | null | undefined,
  mode: 'date' | 'datetime' = 'date'
) {
  const [from, to] = dateRange || []

  return {
    fromDate: from ? normalizeStartDay(from, mode) : null,
    toDate: to ? normalizeEndDay(to, mode) : null
  }
}

/**
 * 📊 Dùng khi lấy khoảng [from → to), loại bỏ ngày `to`
 * - Tăng `to` lên 1 ngày, lấy đầu ngày tiếp theo
 * - BE dùng `MoreThanOrEqual(from) và LessThan(to)`
 */
export function getDateRangeSemiOpen(
  dateRange: [Dayjs | null, Dayjs | null] | null | undefined
) {
  const [from, to] = dateRange || []

  return {
    fromDate: from ? normalizeStartDay(from, 'date') : null,
    toDate: to ? normalizeStartDay(to.add(1, 'day'), 'date') : null
  }
}

/**
 * 🛠️ Parse ISO UTC từ BE → local time (Asia/Ho_Chi_Minh)
 * - Hiển thị đúng giờ cho người dùng
 */
export function parseUtcToLocal(date?: string | null): Dayjs | null {
  return date ? dayjs.utc(date) : null
}
