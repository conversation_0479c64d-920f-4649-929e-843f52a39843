import { configEnv } from '~/@config'
import { IObjectPromise } from '~/@core/dto'
import { AxiosHttpClient, IAxiosRequestOptions } from '~/@core/network/axios-http-client'
import { KeyHeader, KeyLocalStore } from '~/common/constants'
import { environment } from '~/environments/environment'

class ApiService {
  private httpClient: AxiosHttpClient
  readonly backEnd = environment.backEnd
  constructor(config: { baseurl: string; options: IAxiosRequestOptions; interceptors?: IObjectPromise }) {
    this.httpClient = new AxiosHttpClient(config)
  }

  async get<T = any>(endpoint: string, params: any = {}): Promise<T> {
    try {
      params = Object.fromEntries(Object.entries(params).filter(([_, v]) => v !== undefined))
      const res = await this.httpClient.get<T>(endpoint, params)
      return res.data
    } catch (error) {
      throw error
    }
  }
  async getByBody<T = any>(endpoint: string, body: any = {}): Promise<T> {
    try {
      const res = await this.httpClient.getByBody<T>(endpoint, body)
      return res.data
    } catch (error) {
      throw error
    }
  }
  async post<T = any>(endpoint: string, body: any = {}): Promise<T> {
    try {
      const res = await this.httpClient.post<T>(endpoint, body)
      return res.data
    } catch (error) {
      throw error
    }
  }

  async put<T = any>(endpoint: string, body: any = {}): Promise<T> {
    try {
      const res = await this.httpClient.put<T>(endpoint, body)
      return res.data
    } catch (error) {
      throw error
    }
  }

  async delete<T = any>(endpoint: string, body: any = {}): Promise<T> {
    try {
      const res = await this.httpClient.delete<T>(endpoint, body)
      return res.data
    } catch (error) {
      throw error
    }
  }

  async uploadFile<T = any>(endpoint: string, formData: FormData, onUploadProgress?: (event: any) => void): Promise<T> {
    try {
      const res = await this.httpClient.uploadFile<T>(endpoint, formData, onUploadProgress)
      return res.data
    } catch (error) {
      throw error
    }
  }
}

const { ROOT } = configEnv().CONNECTORS
const { baseUrl } = ROOT

export const rootApiService = new ApiService({
  baseurl: baseUrl,
  options: {
    timeout: 60000,
    headers: {
      'Content-Type': 'application/json',
      [KeyHeader.LANG]: 'vi'
    }
  },
  interceptors: {
    [KeyHeader.AUTHORIZATION]: async () => {
      const token = localStorage.getItem(KeyLocalStore.accessToken)
      return `Bearer ${token}`
    }
  }
})

// Tạo service riêng cho auth (không có Authorization header)
export const authApiService = new ApiService({
  baseurl: baseUrl,
  options: {
    timeout: 60000,
    headers: {
      'Content-Type': 'application/json',
      [KeyHeader.LANG]: 'vi'
    }
  }
  // Không có interceptors để không thêm Authorization header
})

export const uploadApiService = new ApiService({
  baseurl: '',
  options: {
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
      [KeyHeader.LANG]: 'vi'
    }
  },
  interceptors: {
    [KeyHeader.AUTHORIZATION]: async () => {
      const token = localStorage.getItem(KeyLocalStore.accessToken)
      return `Bearer ${token}`
    }
  }
})

export const optionalApiService = new ApiService({
  baseurl: '',
  options: {
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
      [KeyHeader.LANG]: 'vi'
    }
  }
})
