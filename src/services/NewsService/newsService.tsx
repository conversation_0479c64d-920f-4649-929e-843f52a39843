import { INews, INewsFilter, INewsList } from '~/dto/news.dto'
import { rootApiService } from '../@common'

export class NewsService {
  constructor() {}

  APIs = {
    LIST: `${rootApiService.backEnd}/api/admin/news`,
    INACTIVE: `${rootApiService.backEnd}/api/admin/news/inactive`,
    ACTIVE: `${rootApiService.backEnd}/api/admin/news/active`,
    CREATE: `${rootApiService.backEnd}/api/admin/news`,
    UPDATE: `${rootApiService.backEnd}/api/admin/news/update`
  }
  async getAllNews(): Promise<INewsList> {
    return await rootApiService.get(this.APIs.LIST, { pageSize: 99999, pageIndex: 1 })
  }

  async getPaginationNews(newsFilter?: INewsFilter) {
    return await rootApiService.get(this.APIs.LIST, newsFilter)
  }

  async setInactiveNews(id: string) {
    return await rootApiService.post(this.APIs.INACTIVE, { id })
  }

  async setActiveNews(id: string) {
    return await rootApiService.post(this.APIs.ACTIVE, { id })
  }

  async updateNews(news: INews) {
    return await rootApiService.post(this.APIs.UPDATE, news)
  }

  async createNews(news: INews) {
    return await rootApiService.post(this.APIs.CREATE, news)
  }
}

export const newsService = new NewsService()
