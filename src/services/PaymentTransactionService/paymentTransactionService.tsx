import {
  ISubscriptionPlan,
  ISubscriptionPlanFilter,
  ISubscriptionPlanResponse
} from '~/dto/subscriptionPlan.dto'
import { rootApiService } from '../@common'
import { enumData } from '~/common/enums/enumData'
import { IPaymentTransaction, IPaymentTransactionFilter } from '~/dto/paymentTransaction.dto'

export class PaymentTransactionService {
  constructor() {}

  APIs = {
    LIST: `${rootApiService.backEnd}/api/admin/payment/pagination`,
    DETAIL: `${rootApiService.backEnd}/api/admin/payment/detail`
  }

  async getAll(): Promise<ISubscriptionPlanResponse> {
    return await rootApiService.post(this.APIs.LIST, {
      pageSize: enumData.Page.pageSizeMax,
      pageIndex: 1
    })
  }

  //pagination
  async getPagination(filter: IPaymentTransactionFilter): Promise<ISubscriptionPlanResponse> {
    return await rootApiService.post(this.APIs.LIST, filter)
  }

  async getDetail(id: string): Promise<IPaymentTransaction> {
    return await rootApiService.post(this.APIs.DETAIL, { id })
  }
}

export const paymentTransactionService = new PaymentTransactionService()
