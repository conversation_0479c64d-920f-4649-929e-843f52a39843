import { ISubscriptionPlanResponse } from '~/dto/subscriptionPlan.dto'
import { rootApiService } from '../@common'
import { enumData } from '~/common/enums/enumData'
import { IPaymentTransaction, IPaymentTransactionFilter } from '~/dto/paymentTransaction.dto'
import { IOrder, IOrderFilter, IOrderRespone } from '~/dto/order.dto'

export class OrderService {
  constructor() {}

  APIs = {
    LIST: `${rootApiService.backEnd}/api/admin/order/pagination`,
    DETAIL: `${rootApiService.backEnd}/api/admin/order/detail`
  }

  async getAll(): Promise<IOrderRespone> {
    return await rootApiService.post(this.APIs.LIST, {
      pageSize: enumData.Page.pageSizeMax,
      pageIndex: 1
    })
  }

  //pagination
  async getPagination(filter: IOrderFilter): Promise<IOrderRespone> {
    return await rootApiService.post(this.APIs.LIST, filter)
  }

  async getDetail(id: string): Promise<IOrder> {
    return await rootApiService.post(this.APIs.DETAIL, { id })
  }
}

export const orderService = new OrderService()
