import { rootApiService } from '../@common'
import { IApiDocContentCreate, IApiDocContentUpdate } from '~/dto/apiDocumentContent'

class ApiDocumentService {
  readonly APIs = {
    LIST: `${rootApiService.backEnd}/api/admin/api-doc-content/list`,
    DETAIL: `${rootApiService.backEnd}/api/admin/api-doc-content/detail`,
    CREATE: `${rootApiService.backEnd}/api/admin/api-doc-content/create`,
    UPDATE: `${rootApiService.backEnd}/api/admin/api-doc-content/update`,
    UPLOAD_IMAGE: `${rootApiService.backEnd}/api/admin/upload/upload_single_s3`
  }

  async getApiDocumentList() {
    return rootApiService.get(this.APIs.LIST, { pageSize: 99999, pageIndex: 1 })
  }

  async getApiDocumentDetail(id: string) {
    return rootApiService.get(this.APIs.DETAIL, { id })
  }

  async createApiDocument(payload: IApiDocContentCreate) {
    return rootApiService.post(this.APIs.CREATE, payload)
  }

  async updateApiDocument(payload: IApiDocContentUpdate) {
    return rootApiService.post(this.APIs.UPDATE, payload)
  }

  async uploadImage(payload: FormData) {
    return rootApiService.uploadFile(this.APIs.UPLOAD_IMAGE, payload)
  }
}

export const apiDocumentService = new ApiDocumentService()
