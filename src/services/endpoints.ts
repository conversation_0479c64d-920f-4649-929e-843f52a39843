export const endpoints_customer = {
  create: '/api/admin/customers/create'.trim(),
  list: '/api/admin/customers/list'.trim(),
  delete: '/api/admin/customers/delete'.trim(),
  update: '/api/admin/customers/update'.trim(),
  listAll: '/api/admin/customers/list-all'.trim()
}

export const endpoints_product = {
  create: '/api/admin/products/create'.trim(),
  list: '/api/admin/products/list'.trim(),
  delete: '/api/admin/products/delete'.trim(),
  update: '/api/admin/products/update'.trim(),
  listAll: '/api/admin/products/list-all'.trim(),
  detail: '/api/admin/products/detail'.trim()
}

export const endpoints_provinces = {
  list: '/api/publics/provinces-list'.trim(),
  listDistrict: '/api/publics/districts-list'.trim(),
  listWard: '/api/publics/wards-list'.trim(),
  fullAddress: '/api/publics/full-address'.trim()
}

export const endpoints_report = {
  dashboard: '/api/admin/reports/dashboard'.trim()
}

export const endpoints_upload = {
  uploadSingle: '/api/admin/upload/upload_single_s3'.trim(),
  uploadMutiple: '/api/admin/upload/upload_mutiple_s3'.trim()
}

export const endpoints_survey_feedback = {
  list: '/survey-feedback',
  getById: '/survey-feedback',
  create: '/survey-feedback',
  update: '/survey-feedback',
  delete: '/survey-feedback',
  uploadImage: '/survey-feedback/upload'
}
