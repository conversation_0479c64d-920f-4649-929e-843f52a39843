import { ISubscriptionPlan, ISubscriptionPlanFilter, ISubscriptionPlanResponse } from '~/dto/subscriptionPlan.dto'
import { rootApiService } from '../@common'
import { enumData } from '~/common/enums/enumData'
import { IPaymentTransaction, IPaymentTransactionFilter } from '~/dto/paymentTransaction.dto'

export class AdvisoryService {
  constructor() {}

  APIs = {
    //CRUD
    LIST: `${rootApiService.backEnd}/api/admin/advisory/pagination`,
    DETAIL: `${rootApiService.backEnd}/api/admin/advisory/detail`,
    CREATE: `${rootApiService.backEnd}/api/admin/advisory`,
    UPDATE: `${rootApiService.backEnd}/api/admin/advisory/update`
  }

  async getAll(): Promise<ISubscriptionPlanResponse> {
    return await rootApiService.post(this.APIs.LIST, {
      pageSize: enumData.Page.pageSizeMax,
      pageIndex: 1
    })
  }

  //pagination
  async getPagination(filter: IPaymentTransactionFilter): Promise<ISubscriptionPlanResponse> {
    return await rootApiService.post(this.APIs.LIST, filter)
  }

  async getDetail(id: string): Promise<IPaymentTransaction> {
    return await rootApiService.post(this.APIs.DETAIL, { id })
  }

  async create(advisory: any) {
    return await rootApiService.post(this.APIs.CREATE, advisory)
  }

  async update(advisory: any) {
    return await rootApiService.post(this.APIs.UPDATE, advisory)
  }
}

export const advisoryService = new AdvisoryService()
