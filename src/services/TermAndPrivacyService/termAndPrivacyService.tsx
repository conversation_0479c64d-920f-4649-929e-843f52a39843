import { ISubscriptionPlan, ISubscriptionPlanFilter, ISubscriptionPlanResponse } from '~/dto/subscriptionPlan.dto'
import { rootApiService } from '../@common'
import { enumData } from '~/common/enums/enumData'
import { ITermsAndPrivacyResponse, ITermsAndPrivacyFilter, ITermsAndPrivacy } from '~/dto/termAndPrivacy'

export class TermsAndPrivacyService {
  constructor() {}

  APIs = {
    LIST: `${rootApiService.backEnd}/api/admin/term/pagination`,
    DETAIL: `${rootApiService.backEnd}/api/admin/term/detail`,
    INACTIVE: `${rootApiService.backEnd}/api/admin/term/inactive`,
    ACTIVE: `${rootApiService.backEnd}/api/admin/term/active`,
    CREATE: `${rootApiService.backEnd}/api/admin/term`,
    UPDATE: `${rootApiService.backEnd}/api/admin/term/update`
  }

  async getAll(): Promise<ITermsAndPrivacyResponse> {
    return await rootApiService.post(this.APIs.LIST, {
      pageSize: enumData.Page.pageSizeMax,
      pageIndex: 1
    })
  }
  //create
  async create(termsAndPrivacy: ITermsAndPrivacy) {
    return await rootApiService.post(this.APIs.CREATE, termsAndPrivacy)
  }

  async pagination(termsAndPrivacyFilter?: ITermsAndPrivacyFilter) {
    return await rootApiService.post(this.APIs.LIST, termsAndPrivacyFilter)
  }

  async getDetail(id: string) {
    return await rootApiService.post(this.APIs.DETAIL, { id })
  }

  async setInactive(id: string) {
    return await rootApiService.post(this.APIs.INACTIVE, { id })
  }

  async setActive(id: string) {
    return await rootApiService.post(this.APIs.ACTIVE, { id })
  }

  async update(termsAndPrivacy: ITermsAndPrivacy) {
    return await rootApiService.post(this.APIs.UPDATE, termsAndPrivacy)
  }
}

export const termsAndPrivacyService = new TermsAndPrivacyService()
