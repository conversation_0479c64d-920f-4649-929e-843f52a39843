import {
  ISubscriptionPlan,
  ISubscriptionPlanFilter,
  ISubscriptionPlanResponse
} from '~/dto/subscriptionPlan.dto'
import { rootApiService } from '../@common'
import { enumData } from '~/common/enums/enumData'

export class SubscriptionPlanService {
  constructor() {}

  APIs = {
    LIST: `${rootApiService.backEnd}/api/admin/package-plans/pagination`,
    DETAIL: (id: string) => {
      return `${rootApiService.backEnd}/api/admin/package-plans/${id}`
    },
    INACTIVE: `${rootApiService.backEnd}/api/admin/package-plans/inactive`,
    ACTIVE: `${rootApiService.backEnd}/api/admin/package-plans/active`,
    CREATE: `${rootApiService.backEnd}/api/admin/package-plans`,
    UPDATE: `${rootApiService.backEnd}/api/admin/package-plans/update`
  }
  async getAll(): Promise<ISubscriptionPlanResponse> {
    return await rootApiService.post(this.APIs.LIST, {
      pageSize: enumData.Page.pageSizeMax,
      pageIndex: 1
    })
  }

  async pagination(subscriptionPlanFilter?: ISubscriptionPlanFilter) {
    return await rootApiService.post(this.APIs.LIST, subscriptionPlanFilter)
  }

  async getDetail(id: string) {
    return await rootApiService.get(this.APIs.DETAIL(id))
  }

  async setInactive(id: string) {
    return await rootApiService.post(this.APIs.INACTIVE, { id })
  }

  async setActive(id: string) {
    return await rootApiService.post(this.APIs.ACTIVE, { id })
  }

  async update(subscriptionPlan: ISubscriptionPlan) {
    return await rootApiService.post(this.APIs.UPDATE, subscriptionPlan)
  }

  async create(subscriptionPlan: ISubscriptionPlan) {
    return await rootApiService.post(this.APIs.CREATE, subscriptionPlan)
  }
}

export const subscriptionPlanService = new SubscriptionPlanService()
