import { environment } from '~/environments/environment'
import { rootApiService } from '../@common'
import { IMember, IMemberFilter, IMemberList } from '~/dto/member.dto'
import { IMemberConfigPackageResponse, IMemberSubscriptionPlan, IMemberSubscriptionPlanResponse, ISubscriptionPlan } from '~/dto/subscriptionPlan.dto'
import { IOrder, IOrderRespone } from '~/dto/order.dto'

class ConfigPackageService {
  constructor() {}
  backEnd = environment.backEnd
  APIs = {
    LOGS: `${this.backEnd}/api/admin/config-package/logs`,
    DETAILS: `${this.backEnd}/api/admin/config-package/detail`
  }

  async getConfigPackageLogs(body: any) {
    return rootApiService.post(`${this.APIs.LOGS}`, body)
  }

  async getConfigPackageDetails(id: string) {
    return rootApiService.post(`${this.APIs.DETAILS}`, {id})
  }
}

export const configPackageService = new ConfigPackageService()
