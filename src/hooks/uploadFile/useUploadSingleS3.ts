import { useMutation } from '@tanstack/react-query'
import { notification } from 'antd'
import { rootApiService } from '~/services'

const apis = {
  UPLOAD_IMAGE: `${rootApiService.backEnd}/api/admin/upload/upload_single_s3`
}

type variables = {
  file: File
}

const useUploadSingleS3 = () => {
  const { isPending, isError, data, error, mutateAsync, mutate } = useMutation({
    mutationFn: (variables: variables) => rootApiService.post(apis.UPLOAD_IMAGE, variables),
    onError: (e: any) => {
      if (e?.message === 'Network Error') {
        notification.error({
          message: 'Cảnh báo',
          description: 'Không có kết nối mạng. Vui lòng kiểm tra lại'
        })
        return
      }
      notification.error({
        message: 'Cảnh báo',
        description: e?.response?.data?.message ?? '<PERSON><PERSON> có lỗi xảy ra vui lòng thử lại sau'
      })
    }
  })

  return {
    isPending,
    isError,
    data: data?.data,
    error,
    mutate,
    mutateAsync
  }
}

export default useUploadSingleS3
