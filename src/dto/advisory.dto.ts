export interface IAdvisory {
  id?: string
  code?: string
  name?: string
  description?: string
  status?: string
  createdAt?: string
  updatedAt?: string
}

//filter
export interface IAdvisoryFilter {
  code?: string
  name?: string
  status?: string
  createdAt?: string
  updatedAt?: string
  pageSize?: number
  pageIndex?: number
}

//respone
export interface IAdvisoryResponse {
  data: IAdvisory[]
  total: number
}
