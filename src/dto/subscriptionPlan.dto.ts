//respone
export interface ISubscriptionPlanResponse {
  data: ISubscriptionPlan[]
  total: number
}

export interface ISubscriptionPlan {
  code: string
  isTrial: boolean
  createdBy: string
  createdDate: string
  currency: string
  description: string
  details: any[]
  id: string
  isMostPopular: true
  name: string
  note: string
  originalPrice: number
  planeTypePayment: string
  quantity: number
  salePrice: number
  status: string
  stripePriceId: null
  updatedBy: null
  updatedDate: string
}

//filter
export interface ISubscriptionPlanFilter {
  code?: string
  name?: string
  memberId?: string
  isActive?: boolean
  status?: string
  byteLimit?: number
  pageIndex?: number
  pageSize?: number
}

export interface IMemberSubscriptionPlan {
  id?: string
  createdDate?: string
  updatedDate?: string
  createdBy: null
  updatedBy: null
  memberId?: string
  orderId?: string
  packagePlanId?: string
  expiredDate?: null
  initialTransactionLimit?: number
  currentTransaction?: number
  initialConfigLimit?: number
  currentConfig?: number
  status?: string
  packageDetails?: ISubscriptionPlan
}

export interface IMemberSubscriptionPlanResponse {
  data: IMemberSubscriptionPlan[]
  total: number
}

//filter
export interface ICustomerSubscriptionPlanFilter {
  name?: string
  isActive?: boolean
  activationDate?: string
  expirationDate?: string
  pageIndex?: number
  pageSize?: number
}

export interface IMemberConfigPackage {
  code?: string
  createdBy?: string
  createdDate?: string
  description?: string
  id?: string
  memberId?: string
  name?: string
  packagePlanId?: string
  status?: string
  logs?: ILogsResponse
  details?: IMemberConfigPackageDetail
  updatedBy?: string
  updatedDate?: string
}

export interface ILogsResponse {
  data: ILogs[]
  total: number
}

export interface ILogs {
  configId?: string
  createdBy?: string
  createdDate?: string
  host?: string
  id?: string
  isTest?: boolean
  memberId?: string
  memberPackageId?: string
  method?: string
  request?: any
  response?: any
  statusCode?: number
  updatedBy?: string
  updatedDate?: string
  url?: string
}

export interface IMemberConfigPackageResponse {
  data: IMemberConfigPackage[]
  total: number
}

export interface IMemberConfigPackageDetail {
  id?: string
  createdDate?: string
  updatedDate?: string
  createdBy?: string
  updatedBy?: string
  version?: number
  memberId?: string
  packagePlanId?: string
  code?: string
  name?: string
  description?: string
  status?: string
  apiInfo?: any
  fields?: any[]
}
