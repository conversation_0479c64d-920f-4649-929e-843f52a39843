export class RemoveImageDto {
  urlImage: string
}

export class IdNumberReq {
  id: number
}
export class UUIDReq {
  id: string
}

export class MultipartFile {
  fieldname: string
  originalname: string
  encoding: string
  mimetype: string
  buffer: Buffer
  size: number
}

export interface PrimaryBaseEntity {
  id?: string
  createdDate?: Date | string
  updatedDate?: Date | string
  createdBy?: string | null
  updatedBy?: string | null
  version?: number
}
