export interface INews {
  id: string
  code: string
  title: string
  categoryName: string
  contentHtml: string
  image?: string
  status: string
  createdAt: string
  updatedAt: string
}

//data
export interface INewsList {
  data: INews[]
  total: number
}

//filter
export interface INewsFilter {
  code?: string
  title?: string
  categoryName?: string
  status?: string
  createdAt?: string
  updatedAt?: string
  pageSize?: number
  pageIndex?: number
}
