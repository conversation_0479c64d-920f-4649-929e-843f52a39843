export interface IPaymentTransaction {
  id?: string
  code?: string
  amount?: number
  type?: string
  status?: string
  member?: any
  refId?: string
  sessionRefId?: string
  clientSecret?: string
  customerCode?: string
  customerName?: string
  servicePlan?: string
  securitySetting?: string
  paymentProvider?: string
  paymentMethod?: string
  createdDate?: string
  updatedDate?: string
}

//filter
export interface IPaymentTransactionFilter {
  id?: string
  code?: string
  amount?: number
  type?: string
  status?: string
  customerCode?: string
  fullName?: string
  servicePlan?: string
  securitySetting?: string
  createdAt?: string
  pageIndex?: number
  pageSize?: number
  createdDateFrom?: string
  createdDateTo?: string
  transactionDateFrom?: string
  transactionDateTo?: string
}
