export interface IOnChainTransaction {
  id?: string
  configCode?: string
  email?: string
  ownerAddressKey?: string
  status?: string
  transactionFee?: string
  transactionHash?: string
  dataOnChain?: {}
  dataOfChain?: {}
  createdDate?: string
  updatedDate?: string
}

//filter
export interface IOnChainTransactionFilter {
  id?: string
  configCode?: string
  pageSize?: number
  pageNumber?: number
  email?: number
  status?: string
  transactionFee?: string
  transactionHash?: string
  createdDate?: string
  updatedDate?: string
}

export interface IOnChainTransactionRespone {
  data?: IOnChainTransaction[]
  total?: number
}
