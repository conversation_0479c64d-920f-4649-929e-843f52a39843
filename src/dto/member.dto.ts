import { NSMember } from '~/common/enums/NSMember'
import { ISubscriptionPlan } from './subscriptionPlan.dto'
import { IOrder } from './order.dto'

export interface IMember {
  id: string
  email: string
  password: string
  fullName: string
  avatar: string
  phone: string
  status: NSMember.EMemberStatus
  statusValidate: NSMember.EMemberValidateStatus
  orders?: IOrder[]
  packages?: ISubscriptionPlan[]
}

export interface IMemberList {
  data?: IMember[]
  total?: number
}

//filter
export interface IMemberFilter {
  email?: string
  fullName?: string
  status?: NSMember.EMemberStatus
  pageSize?: number
  pageIndex?: number
}
