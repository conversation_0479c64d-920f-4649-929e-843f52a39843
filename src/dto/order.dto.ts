//respone
export interface IOrderRespone {
  data: IOrder[]
  total: number
}

export interface IOrder {
  id: string
  code: string
  email: string
  member: any
  paymentMethod: string
  paymentStatus: string
  customerCode: string
  customerName: string
  totalPrice: number
  vat: number
  totalPriceVat: number
  currency: string
  paymentDate: string
  status: string
  description: string
  createdAt: string
  updatedAt: string
}

//filter
export interface IOrderFilter {
  code?: string
  name?: string
  status?: string
  memberId?: string
  paymentDateFrom?: Date
  paymentDateTo?: Date
  createdDateFrom?: Date
  createdDateTo?: Date
  orderDate?: string
  createdAt?: string
  updatedAt?: string
  pageIndex?: number
  pageSize?: number
}
