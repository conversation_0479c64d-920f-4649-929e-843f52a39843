import helper from "../helper/helper";

const global = {
  table: {
    size: "small",
    bordered: true,
    scroll: { x: true },
    style: {
      whiteSpace: "nowrap"
    },
    pagination: {
      showSizeChanger: true,
      // showTotal: (sum: any) => <div>{ sum } < /div>
    }
  },
  tableNoPagination: {
    size: "small",
    bordered: true,
    scroll: { x: true },
    style: {
      whiteSpace: "nowrap",
    },
    pagination: false,
  },
  tableRow: {
    ellipsis: true,
  },
  row: {
    gutter: [32, 0],
  },
  colHalf: {
    xxl: 12,
    xl: 12,
    lg: 12,
    md: 12,
    xs: 24,
  },
  colFull: {
    span: 24,
  },
  form: {
    labelAlign: "right",
    layout: "vertical",
    scrollToFirstError: true,
  },
  formItem: {
    className: "custom-form-item",
  },
  selectSearch: {
    showSearch: true,
    filterOption: (input: any, option: any) =>
      stringToASCII(option.children).indexOf(stringToASCII(input)) >= 0
  },
  inputNumber: {
    formatter: value => (value && !isNaN(value)) ? helper.numberToString(value) : "0",
    parser: value => {
      let result = helper.stringToNumber(value);
      return !isNaN(result) ? result : 0;
    },
  },
  inputNumberNegative: {
    formatter: value => (value) ? helper.numberToString(value) : "",
  },
  inputNumberAllowEmpty: {
    formatter: value => !isNaN(value) ? `${value}`.replace(/\B(?=([-]\d{3})+(?!\d))/g, ',') : "",
    parser: value => !!value ? `${value}`.replace(/\$\s?|(,*)/g, '') : value,
  },
}

const numberFormatter = (number: number) => global.inputNumber.formatter(Math.round(number));

function stringToASCII(str: string) {
  try {
    return str.toLowerCase()
      .replace(/[àáảãạâầấẩẫậăằắẳẵặ]/g, 'a')
      .replace(/[èéẻẽẹêềếểễệ]/g, 'e')
      .replace(/[đ]/g, 'd')
      .replace(/[ìíỉĩị]/g, 'i')
      .replace(/[òóỏõọôồốổỗộơờớởỡợ]/g, 'o')
      .replace(/[ùúủũụưừứửữự]/g, 'u')
      .replace(/[ỳýỷỹỵ]/g, 'y')
  } catch (e) {
    return ''
  }
}

const format = {
  date: "DD/MM/YYYY",
  dateTime: "DD/MM/YYYY HH:mm",
  dateTimes: "DD/MM/YYYY HH:mm:ss",
  timeDates: "HH:mm:ss DD/MM/YYYY",
  year: "YYYY",
  month: "MM/YYYY",
  quarter: "qQ/YYYY",
}

export {
  format,
  global,
  stringToASCII,
  numberFormatter,
}