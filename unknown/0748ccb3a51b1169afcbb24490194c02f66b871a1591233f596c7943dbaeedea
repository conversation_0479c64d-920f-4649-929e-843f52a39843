


import { Table } from 'antd';
import { ColumnGroupType, ColumnsType } from 'antd/lib/table';
import React, { FC, useState } from 'react';
import { GridOption } from './models';

const GridControl = <T extends object = any>(gridOption: GridOption<T>) => {
  const { columnDefs } = gridOption;

  const [data, setData] = useState([])

  const mapColumns = (): ColumnGroupType<T>[] => {
    return columnDefs.map(colDef => {
      const item: ColumnGroupType<T> = {
        children: []
      };
      item.title = colDef.headerName;
      item.width = colDef.width || colDef.minWidth;
      item.children = [
        {
          title: colDef.headerName,
          width: colDef.width || colDef.minWidth,
          dataIndex: colDef.field as string
        }
      ]
      return item;
    })
  }

  return (
    <Table<T>
      columns={mapColumns()}
      dataSource={data}
    >
    </Table>
  )
}

export default GridControl
